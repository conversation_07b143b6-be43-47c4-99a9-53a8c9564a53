# 需求理解与确认纪要

---

## 标准文档页眉

| 属性 | 值 |
| :--- | :--- |
| **项目名称** | 茂名市地质灾害预警平台 |
| **文档标题** | 需求理解与确认纪要 |
| **文档版本** | 1.0 |
| **创建日期** | 2025-07-11 |
| **AI助手** | Augment Agent (开发者: Augment Code) |
| **用户代表** | 梁铭显 |
| **依据文档** | 《市场与用户研究报告》V1.1、《产品愿景与目标》V1.1、《需求框架与Epic识别报告》V1.2、《产品路线图》V1.2 |

---

## 1. 目标平台确认

**主要目标平台：**
1. **Web端**：网站查询服务（自适应响应设计）
   - 通过电脑访问网站
   - 提供根据区县、镇街筛选位置查询功能
   - 基于天地图服务的地图展示

2. **移动端**：通过微信公众号跳转至Web端网站
   - 除了Web端的筛选功能外，还需要增加定位功能
   - 移动端优化的用户界面
   - 触屏操作友好

3. **管理后台**：响应式设计
   - 可在PC和手机上正常显示
   - 支持系统管理、数据管理、预警发布等功能

**设计优先级：** Web端（自适应响应）> 移动端优化 > 管理后台

---

## 2. 核心用户画像

### 2.1 公众用户（主要用户群体）
- **人口统计特征：** 18-65岁，各教育层次，地质灾害风险区域居民
- **地理分布：** 茂名市全域，重点关注地质灾害风险区域，涉及90个镇街
- **关键特征与需求：** 关注家庭安全，希望了解居住地风险状况
- **主要使用场景：** 查询居住地或关注地点的地质灾害风险
- **规模估算：** 约472万人

### 2.2 政府管理用户（内部用户）
- **人口统计特征：** 30-50岁，本科以上学历，政府工作人员
- **地理分布：** 茂名市自然资源局及下属机构
- **关键特征与需求：** 提升工作效率，有效维护和管理地质灾害数据
- **主要使用场景：** 数据管理、预警信息发布、系统管理
- **规模估算：** 核心用户约10人

### 2.3 基层工作人员（查询用户）
- **人口统计特征：** 25-45岁，大专以上学历，镇街工作人员
- **地理分布：** 茂名市各镇街道办事处
- **关键特征与需求：** 快速获取风险信息，及时响应上级指令
- **主要使用场景：** 查询辖区风险状况，协助预警信息传达
- **规模估算：** 约500-900人

---

## 3. 主要使用场景/用户故事

### 3.1 公众查询场景
- **用户故事：** 作为茂名市民，我想通过网站或微信公众号查询我所在位置的地质灾害风险，以便了解家庭安全状况
- **涉及功能点：** 位置定位、地图查询、风险信息展示、查询结果解读

### 3.2 政府管理场景
- **用户故事：** 作为防灾科工作人员，我想通过管理后台管理地质灾害数据和发布预警信息，以便提升工作效率
- **涉及功能点：** 用户登录、数据管理、预警发布、系统管理

### 3.3 基层工作场景
- **用户故事：** 作为镇街工作人员，我想快速查询辖区的地质灾害风险状况，以便及时响应和处理
- **涉及功能点：** 快速查询、区域筛选、风险信息获取

---

## 4. 核心功能需求列表

### 4.1 公众查询服务模块（优先级：高）
- **网站查询功能**：基于天地图的地图展示和位置查询
- **移动端定位功能**：GPS定位查询地质灾害风险
- **查询结果展示**：地质灾害点和风险防范区信息可视化
- **筛选功能**：根据区县、镇街筛选位置

### 4.2 系统管理模块（优先级：高）
- **用户认证与授权**：安全登录、会话管理、权限控制
- **用户管理**：用户信息管理、状态管理、角色分配
- **权限管理**：角色定义、功能权限分配、访问控制
- **操作日志管理**：系统操作记录、查询审计

### 4.3 数据管理模块（优先级：中）
- **地质灾害点管理**：灾害点信息的增删改查
- **风险防范区管理**：防范区信息的增删改查
- **数据导入导出**：批量数据处理、SHP格式支持

### 4.4 预警发布模块（优先级：中）
- **预警信息管理**：预警信息编辑、等级管理、历史记录
- **多渠道发布**：微信公众号发布、短信发布（可选）

---

## 5. 非功能性需求

- **性能要求**：查询响应时间<3秒，系统可用性99.5%+
- **安全要求**：满足政府系统安全合规要求，数据安全保障
- **可用性要求**：界面简洁易用，操作流程清晰
- **兼容性要求**：支持主流浏览器，移动端适配

---

## 6. 品牌调性与产品定位

- **产品定位**：茂名市民身边的地质安全守护者
- **品牌关键词**：安全、专业、便民、可靠、透明
- **产品愿景**：让地质灾害风险信息触手可及，让安全防护深入人心
- **服务理念**：公益性质，免费便民服务

---

## 7. 设计主题偏好

- **公众查询网站主题**：明亮主题（白色/浅色背景）
  - 原因：服务472万用户，年龄跨度大，需要清晰易读的信息展示
  - 要求：高对比度、良好可读性、符合政府网站规范
- **管理后台主题**：暗黑主题
  - 原因：专业用户使用，符合现代管理系统设计趋势
  - 要求：专业、高效、减少视觉疲劳
- **色彩倾向**：专业、稳重、具有安全感的色彩搭配
- **风格要求**：现代、简洁、专业、易用

---

## 8. 用户明确的偏好与排除项

### 8.1 用户偏好
- **功能聚焦**：专注于数据管理和信息发布，避免功能过度复杂化
- **用户体验**：重点优化公众查询界面，确保操作简单、信息准确
- **技术路线**：采用轻量化技术方案，确保系统稳定可靠、易于维护

### 8.2 排除项
- **不涉及功能**：实时监测功能、复杂的数据分析报表
- **避免风格**：过于复杂的界面设计、冗余的功能展示

---

## 9. 待澄清问题及解答记录

**Q1：** 目标平台确认？
**A1：** 确认为Web端（自适应响应）+ 移动端（微信公众号跳转）+ 管理后台（响应式设计）

**Q2：** 移动端特殊需求？
**A2：** 移动端除了Web端的筛选功能外，还需要增加定位功能

**Q3：** 管理后台设备支持？
**A3：** 响应式设计，可在PC和手机上正常显示

---

## 10. 设计范围与边界

### 10.1 本次设计包含

#### 10.1.1 公众查询服务界面（Epic 1）
- 首页/查询主界面（Web端自适应响应）
- 移动端查询界面（增加定位功能）
- 查询结果展示界面

#### 10.1.2 系统管理模块界面（Epic 4）
- 登录界面
- 系统主界面/仪表板
- 用户管理界面（用户列表、新增用户、编辑用户）
- 权限管理界面（角色管理、权限分配）
- 操作日志查询界面
- 系统配置界面

#### 10.1.3 数据管理模块界面（Epic 2）
- 地质灾害点管理界面（列表、新增、编辑、详情）
- 风险防范区管理界面（列表、新增、编辑、详情）
- 数据导入界面
- 数据导出界面

#### 10.1.4 预警发布模块界面（Epic 3）
- 预警信息管理界面（预警列表、新增预警、编辑预警）
- 预警发布界面
- 发布历史查询界面

#### 10.1.5 通用界面
- 404错误页面
- 500错误页面
- 无权限访问页面
- 密码重置界面
- 个人信息修改界面
- 主入口展示页面（index.html）

### 10.2 本次不涉及范围
- 实时监测功能界面
- 复杂数据分析报表界面
- 短信发送管理界面（优先级P3，可延后实现）
- 系统监控和运维界面

---

## 11. 本次确认的设计目标总结

本次设计的核心方向是为茂名市地质灾害预警平台创建一套专业、简洁、易用的高保真HTML原型，重点服务于472万群众的地质灾害风险查询需求和政府部门的数据管理需求。设计将采用暗黑主题，体现专业性和安全感，确保在Web端、移动端和管理后台都能提供优秀的用户体验。

---

## 标准用户审核模块

- **用户意见：** [留空待用户填写]
- **确认状态：** [ ] 待确认 [ ] 已确认
- **确认日期：** YYYY-MM-DD
- **用户签名/确认记录：** [留空待用户处理或记录用户明确同意的文本]
