<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茂名自然资源 - 微信公众号</title>
    <link rel="stylesheet" href="../common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 移动端特定样式 - 与P002保持完全一致 */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .phone-container {
            width: 375px; /* 19.5:9比例标准宽度 */
            height: 720px; /* 减小高度，保持比例 */
            background-color: #1a1a1a;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background-color: white;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        .phone-notch {
            position: absolute;
            top: 8px; /* 考虑phone-container的padding */
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background-color: #1a1a1a;
            border-radius: 0 0 15px 15px;
            z-index: 9999; /* 提高z-index确保在最顶层 */
        }

        .mobile-content-wrapper {
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: #1C1C1E;
            /* 移除padding-top，采用更精确的安全区域处理 */
        }

        .mobile-header {
            height: 88px; /* 44px导航栏 + 44px状态栏安全区域 */
            background-color: #393A3F;
            border-bottom: 1px solid #2C2C2E;
            display: flex;
            align-items: flex-end; /* 内容对齐到底部，避开刘海屏 */
            justify-content: center;
            padding: 0 16px 12px 16px; /* 底部留12px间距 */
            position: relative;
            z-index: 1000;
        }

        .mobile-title {
            font-size: 17px;
            font-weight: 500;
            color: white;
        }

        /* 状态栏模拟 - 与P002完全一致 */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background-color: #393A3F;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: white;
            z-index: 1002;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
            align-items: flex-end;
        }

        .signal-bar {
            width: 3px;
            height: 8px;
            background-color: white;
            border-radius: 1px;
        }

        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }

        .nav-back {
            position: absolute;
            left: 16px;
            font-size: 18px;
            cursor: pointer;
            color: white;
        }

        .nav-more {
            position: absolute;
            right: 16px;
            font-size: 18px;
            cursor: pointer;
            color: white;
        }

        /* 主要内容区域 - 与P002结构一致 */
        .mobile-content {
            flex: 1;
            background-color: #1C1C1E;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding-bottom: 60px; /* 为底部菜单栏留出空间 */
            position: relative;
        }

        /* 头图文章 - 使用真实图片 */
        .featured-article {
            background-color: #1C1C1E;
            padding: 16px;
            margin-bottom: 8px;
            cursor: pointer;
        }

        .featured-image {
            width: 100%;
            height: 180px;
            border-radius: 8px;
            background-image: url('images/1.png');
            background-size: cover;
            background-position: center;
            margin-bottom: 12px;
        }

        .featured-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .featured-time {
            color: #999;
            font-size: 12px;
            text-align: center;
            margin-bottom: 10px;
        }

        /* 文章列表 - 完全还原截图样式 */
        .article-list {
            background-color: #1C1C1E;
            flex: 1;
            overflow-y: auto;
        }

        .article-item {
            display: flex;
            align-items: flex-start;
            padding: 16px;
            border-bottom: 1px solid #2C2C2E;
            cursor: pointer;
        }

        .article-item:hover {
            background-color: #2C2C2E;
        }

        .article-item:last-child {
            border-bottom: none;
        }

        .article-content {
            flex: 1;
            margin-right: 12px;
        }

        .article-title {
            color: white;
            font-size: 16px;
            line-height: 1.4;
            margin-bottom: 8px;
            font-weight: 400;
        }

        .article-image {
            width: 60px;
            height: 60px;
            background-color: #3C3C3E;
            border-radius: 4px;
            flex-shrink: 0;
            background-size: cover;
            background-position: center;
        }

        /* 底部菜单栏 - 放在手机屏幕内 */
        .bottom-menu {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background-color: #1C1C1E;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            border-top: 1px solid #2C2C2E;
            z-index: 1000;
        }

        .menu-icon-left {
            width: 20px;
            height: 20px;
            border: 2px solid #8E8E93;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8E8E93;
            font-size: 12px;
        }

        .menu-items {
            display: flex;
            gap: 40px;
            flex: 1;
            justify-content: center;
        }

        .bottom-item {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #8E8E93;
            font-size: 14px;
            cursor: pointer;
        }

        .item-icon {
            font-size: 12px;
            color: #8E8E93;
        }

        /* 响应式设计 - 与P002保持一致 */
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

            .phone-container {
                width: 350px;
                height: 672px; /* 调整后的高度 */
            }
        }

        /* 隐藏滚动条 - 与P002保持一致 */
        @media (max-width: 400px) {
            .phone-container {
                display: none !important;
            }
        }

    </style>
</head>
<body>
    <div class="phone-container">
        <div class="phone-notch"></div>
        <div class="phone-screen">
            <div class="mobile-content-wrapper">
                <!-- 状态栏 - 与P002完全一致 -->
                <div class="status-bar">
                    <div class="status-left">
                        <div class="signal-bars">
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                            <div class="signal-bar"></div>
                        </div>
                        <span style="margin-left: 6px;">中国移动</span>
                    </div>
                    <div class="status-right">
                        <span>100%</span>
                        <i class="fas fa-battery-full" style="margin-left: 4px;"></i>
                    </div>
                </div>

                <!-- 移动端头部 -->
                <header class="mobile-header">
                    <i class="fas fa-arrow-left nav-back"></i>
                    <div class="mobile-title">茂名自然资源</div>
                    <i class="fas fa-user nav-more"></i>
                </header>

                <!-- 主要内容区域 -->
                <div class="mobile-content">
                    <!-- 头图文章 -->
                    <div class="featured-article">
                        <div class="featured-time">周一 17:26</div>
                        <div class="featured-image"></div>
                    </div>

                    <!-- 文章列表 - 只显示3篇 -->
                    <div class="article-list">
                        <div class="article-item">
                            <div class="article-content">
                                <div class="article-title">即买即退钟到账！免签+退税"中国游"带火"中国购"</div>
                            </div>
                            <div class="article-image" style="background-image: url('images/2.png');"></div>
                        </div>

                        <div class="article-item">
                            <div class="article-content">
                                <div class="article-title">境外投资者以分配利润直接投资税收抵免政策出台 为投资中国打造更...</div>
                            </div>
                            <div class="article-image" style="background-image: url('images/3.png');"></div>
                        </div>

                        <div class="article-item">
                            <div class="article-content">
                                <div class="article-title">经济政策一线微观察｜从现场新热力到制度新活力，看一应尽球如何助推...</div>
                            </div>
                            <div class="article-image" style="background-image: url('images/4.png');"></div>
                        </div>
                    </div>

                    <!-- 底部菜单栏 - 放在mobile-content内 -->
                    <div class="bottom-menu">
                        <!-- 左侧圆形图标 -->
                        <div class="menu-icon-left">
                            <i class="fas fa-th"></i>
                        </div>

                        <!-- 中间菜单项 -->
                        <div class="menu-items">
                            <div class="bottom-item">
                                <span>平安茂名</span>
                            </div>
                            <div class="bottom-item" id="hazardWarningBtn">
                                <span>灾害预警</span>
                                <i class="fas fa-external-link-alt item-icon"></i>
                            </div>
                            <div class="bottom-item">
                                <span>互动交流</span>
                                <i class="fas fa-external-link-alt item-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // 页面交互事件
        document.addEventListener('DOMContentLoaded', function() {
            // 文章点击事件
            const articleItems = document.querySelectorAll('.article-item');
            articleItems.forEach(item => {
                item.addEventListener('click', function() {
                    const title = this.querySelector('.article-title').textContent;
                    alert(`查看文章：${title}\n此功能正在开发中...`);
                });
            });

            // 底部导航点击事件
            const bottomItems = document.querySelectorAll('.bottom-item');
            bottomItems.forEach(item => {
                item.addEventListener('click', function() {
                    const text = this.querySelector('span').textContent;
                    if (text === '灾害预警') {
                        // 跳转到P002
                        window.location.href = 'mobile-query.html';
                    } else {
                        alert(`点击了：${text}\n此功能正在开发中...`);
                    }
                });
            });

            // 返回按钮点击事件
            document.querySelector('.nav-back').addEventListener('click', function() {
                alert('返回微信聊天列表');
            });

            // 更多按钮点击事件
            document.querySelector('.nav-more').addEventListener('click', function() {
                alert('显示更多选项');
            });

            // 头图文章点击事件
            document.querySelector('.featured-article').addEventListener('click', function() {
                alert('查看文章：粤自然 粤文艺｜耕地守护者（剪纸+诗歌）\n此功能正在开发中...');
            });
        });
    </script>
</body>
</html>
