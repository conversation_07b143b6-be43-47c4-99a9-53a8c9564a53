---
type: "manual"
---

# 移动端开发规范

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档名称 | 移动端开发规范 |
| 文档版本 | v1.0.0 |
| 生效日期 | 2025-06-24 |
| 适用范围 | 所有基于 Flutter 的移动端项目 |
| 核心依赖 | [Effective Dart](https://dart.dev/effective-dart), [编码规范.md](./编码规范.md) |
| 审批人 | 技术负责人 |
| 维护人 | 研发团队 |

## 目录

1.  [概述和适用范围](#1-概述和适用范围)
2.  [核心技术栈与环境配置](#2-核心技术栈与环境配置)
3.  [编码规范 (基于Effective Dart)](#3-编码规范-基于effective-dart)
4.  [静态代码分析与质量工具](#4-静态代码分析与质量工具)
5.  [项目结构规范](#5-项目结构规范)
6.  [UI与组件开发规范](#6-ui与组件开发规范)
7.  [状态管理规范](#7-状态管理规范)
8.  [路由管理规范](#8-路由管理规范)
9.  [网络与数据处理规范](#9-网络与数据处理规范)
10. [错误与日志处理规范](#10-错误与日志处理规范)
11. [测试规范](#11-测试规范)
12. [依赖管理规范](#12-依赖管理规范)
13. [Git工作流规范](#13-git工作流规范)
14. [配置管理规范](#14-配置管理规范)
15. [安全编码规范](#15-安全编码规范)
16. [国际化(i18n)规范](#16-国际化i18n规范)
17. [CI/CD集成规范](#17-cicd集成规范)

---

## 1. 概述和适用范围

### 1.1 目标
本规范旨在为基于 Flutter 的移动端项目提供一套统一的开发标准，以实现：
- **提高代码质量和可读性**：编写清晰、易于理解的代码。
- **增强代码可维护性**：使代码易于修改、扩展和重构。
- **促进团队协作效率**：统一的规范减少沟通成本和代码审查时间。
- **确保应用性能和稳定性**：遵循最佳实践，避免常见性能陷阱。
- **降低项目长期维护成本**：结构化的代码和清晰的文档使项目更易于交接和维护。

### 1.2 适用范围
- 所有新开发的 Flutter 项目。
- 现有项目的重要功能模块开发或重构。
- 第三方库和工具的集成与封装。

### 1.3 强制性要求
- **【强制】** 所有团队成员必须遵循本规范。
- **【强制】** 所有代码提交前必须通过自动化代码格式化和静态分析检查。
- **【强制】** 所有代码必须经过同行评审 (Code Review)。
- **【推荐】** 定期参加 Flutter 相关的技术分享和规范培训。

---

## 2. 核心技术栈与环境配置

### 2.1 Flutter 与 Dart 版本
- **【强制】** 项目应统一使用 Flutter SDK 的特定版本，并在 `README.md` 中明确声明。版本应选用官方发布的稳定版 (Stable Channel)。
- **【强制】** 项目的 Dart SDK 版本由 Flutter SDK 版本决定。所有开发者应确保环境一致。
- **【强制】** `pubspec.yaml` 文件中的 `environment` 字段必须明确指定兼容的 Dart 和 Flutter 版本范围。

```yaml
environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"
```

### 2.2 IDE 与插件推荐
- **【推荐】** 使用 `Visual Studio Code` 或 `Android Studio` / `IntelliJ IDEA` 作为主要开发环境。
- **【强制】** 必须安装官方的 `Flutter` 和 `Dart` 插件。
- **【推荐】** 安装有助于提升开发效率的插件，例如：
    - `Flutter Riverpod Snippets` (如果项目使用Riverpod)
    - `Bloc` (如果项目使用BLoC)
    - `Pubspec Assist`
    - `Error Lens`
    - `GitLens`

### 2.3 关键依赖库选型
- 项目在启动阶段，应由技术负责人确定核心依赖库，并在项目中保持统一。具体方案将在后续章节详细阐述。
- **状态管理**：推荐使用 `Riverpod` 或 `BLoC`。
- **路由管理**：推荐使用 `GoRouter`。
- **网络请求**：推荐使用 `Dio`。
- **本地存储**：推荐使用 `Hive` 或 `Isar`。

---

## 3. 编码规范 (基于Effective Dart)

**本项目所有 Dart 代码必须严格遵循 [Effective Dart](https://dart.dev/effective-dart) 系列指南。** 本章节仅摘录和强调其中最关键的部分。当本规范与 `Effective Dart` 冲突时，以 `Effective Dart` 为准。

### 3.1 代码格式化 (Formatting)
- **【强制】** 所有代码在提交前，必须使用 `flutter format .` 或 IDE 的格式化功能进行格式化。
- **【强制】** 行长度限制在 80 个字符以内。
- **【强制】** 对所有流程控制语句（`if`, `else`, `for`, `while`, `do-while`, `switch`）使用花括号 `{}`，即使只有一行代码。唯一的例外是无 `else` 的单行 `if` 语句。

### 3.2 命名约定 (Identifiers)
- **【强制】** 类型（`class`, `enum`, `extension`, `mixin`, `typedef`）命名使用 `UpperCamelCase`。
  ```dart
  class MyWidget extends StatelessWidget { ... }
  enum Status { loading, success, error }
  ```
- **【强制】** 包、目录、源文件名使用 `lowercase_with_underscores`。
  ```
  lib/
    utils/
      string_utils.dart
  ```
- **【强制】** 库前缀、变量、常量、方法、函数名使用 `lowerCamelCase`。
  ```dart
  import 'dart:math' as math;
  const defaultTimeout = Duration(seconds: 10);
  final urlScheme = RegExp('^([a-z]+):');
  void sendRequest() { ... }
  ```
- **【强制】** 私有成员和顶级声明，在其名称前加下划线 `_`。
  ```dart
  class _MyPrivateClass {
    final String _privateField;
  }
  ```
- **【强制】** 避免使用无意义的前缀字母（如 `k` 表示常量，`m` 表示成员变量）。

### 3.3 导入/导出顺序 (Ordering)
- **【强制】** `import` 和 `export` 语句必须遵循以下顺序，且每个部分之间用空行隔开：
  1. `dart:` 导入
  2. `package:` 导入
  3. 相对路径 (`../`, `./`) 导入
  4. `export` 导出
- **【强制】** 每个部分内部的语句应按字母顺序排序。
  ```dart
  import 'dart:async';

  import 'package:flutter/material.dart';
  import 'package:riverpod/riverpod.dart';

  import 'my_local_util.dart';

  export 'src/my_public_interface.dart';
  ```

### 3.4 文档与注释 (Documentation)
- **【强制】** 所有公开的 API（非私有的成员、类、函数等）都必须使用 `///` 添加文档注释。
- **【推荐】** 为私有的、但逻辑复杂的代码块编写文档注释或普通注释 (`//`)。
- **【强制】** 文档注释应遵循 [Effective Dart: Documentation](https://dart.dev/effective-dart/documentation) 的规范。

---

## 4. 静态代码分析与质量工具

### 4.1 Linter 规则配置
- **【强制】** 项目根目录必须包含 `analysis_options.yaml` 文件，用于配置静态分析规则。
- **【推荐】** 继承 Flutter 社区推荐的严格规则集，例如 `package:flutter_lints` 或 `package:lints/recommended.yaml`，并在此基础上增加更严格的规则。

```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

linter:
  rules:
    # 风格强制
    - always_declare_return_types
    - prefer_single_quotes
    - sort_child_properties_last
    - parameter_assignments
    - prefer_final_locals
    - require_trailing_commas

    # 性能优化
    - avoid_redundant_argument_values

    # 错误避免
    - avoid_dynamic_calls
    - cancel_subscriptions
    - test_types_in_equals
    - unawaited_futures
```

### 4.2 自动化检查工具
- **【推荐】** 使用 `pre-commit` Git 钩子在代码提交前自动运行 `flutter analyze` 和 `flutter format`，确保不合规的代码不会进入代码库。配置方法可参考 `Python 项目编码规范`。
- **【强制】** CI/CD 流程中必须包含代码质量检查步骤 (`flutter analyze --fatal-infos`)，任何警告或错误都将导致流水线失败。
```

## 5. 项目结构规范

为了保证项目的可扩展性和可维护性，所有 Flutter 项目 **【必须】** 采用统一的、分层清晰的目录结构。我们推荐使用以功能（Feature）为导向的结构。

### 5.1 顶级目录结构

```
flutter_project/
├── .github/              # CI/CD 工作流 (例如: GitHub Actions)
├── .vscode/              # VS Code 编辑器配置
├── android/              # Android 原生代码
├── assets/               # 静态资源 (图片, 字体, JSON文件等)
│   ├── fonts/
│   ├── images/
│   └── mock/
├── ios/                  # iOS 原生代码
├── lib/                  # Dart 源代码
│   ├── main.dart         # 应用入口
│   └── src/              # 应用核心代码
├── test/                 # 测试代码
├── pubspec.yaml          # 项目依赖和元数据
└── README.md             # 项目说明
```

### 5.2 `lib/src` 目录结构 (Feature-First)

**【强制】** `lib` 目录下的核心逻辑必须全部放在 `src` 目录中。

```
lib/src/
├── app.dart                  # 应用根Widget (MaterialApp/CupertinoApp)
├── common_widgets/           # 通用、可复用的UI组件
├── constants/                # 应用级常量 (API URL, 默认配置等)
├── data/                     # 数据层
│   ├── models/               # 数据模型 (使用 Freezed 和/或 JsonSerializable)
│   ├── providers/            # 数据提供者 (例如: API客户端, 数据库连接)
│   └── repositories/         # 数据仓库实现
├── domain/                   # 领域层 (业务核心，强制)
│   ├── entities/             # 业务实体 (定义核心业务对象和规则)
│   └── repositories/         # 数据仓库抽象接口
├── features/                 # 按功能模块划分的核心代码
│   └── user_auth/            # 【示例】用户认证功能
│       ├── data/             # 功能特定的数据层
│       │   ├── models/
│       │   └── repositories/
│       ├── presentation/     # 功能的表现层 (UI & State)
│       │   ├── bloc/         # Bloc/Cubit 状态管理
│       │   ├── screens/      # 屏幕/页面
│       │   └── widgets/      # 功能特定的UI组件
│       └── ...               # 其他功能模块
├── routing/                  # 路由管理 (使用 GoRouter)
│   ├── app_router.dart       # 路由配置
│   └── app_routes.dart       # 路由定义
├── theme/                    # 应用主题与样式
│   ├── app_theme.dart        # ThemeData 定义
│   └── app_colors.dart       # 颜色常量
└── utils/                    # 通用工具函数 (日期格式化, 校验器等)
```

## 6. UI与组件开发规范

### 6.1 组件化原则
- **【强制】** 优先拆分和封装可复用的UI组件，并放置于 `src/common_widgets/` 目录。
- **【强制】** 保持Widget的单一职责。一个Widget应只做一件事。
- **【推荐】** 尽可能使用 `StatelessWidget`。只有当Widget需要管理内部状态且该状态不影响应用其他部分时，才使用 `StatefulWidget`。

### 6.2 命名规范
- **文件命名**：使用小写蛇形命名法 (`snake_case`)。例如: `user_profile_screen.dart`, `primary_button.dart`。
- **Widget类命名**：使用大写驼峰命名法 (`CamelCase`)。根据文件内容和用途，后缀可以是 `Screen`, `View`, `Widget`, `Dialog` 等。例如: `UserProfileScreen`, `PrimaryButton`。

### 6.3 主题与样式
- **【强制】** 所有颜色、字体样式、边距等UI常量必须在 `src/theme/` 目录下统一定义，并通过 `Theme.of(context)` 获取。
- **【严禁】** 在Widget的 `build` 方法中硬编码颜色值、字体大小等样式信息。

```dart
// 正确示例: 通过Theme获取
Text('Hello', style: Theme.of(context).textTheme.headline1);
Container(color: Theme.of(context).primaryColor);

// 错误示例: 硬编码
Text('Hello', style: TextStyle(fontSize: 24.0, color: Color(0xFFFFFFFF)));
```

## 7. 状态管理规范

**【强制】** 项目统一使用 **BLoC** (`bloc` & `flutter_bloc`) 作为状态管理方案。

### 7.1 核心模型
- **【强制】** 所有状态管理场景均使用完整的BLoC模型（Events -> BLoC -> States）。

### 7.2 BLoC职责
- **【强制】** BLoC的职责必须严格限定于管理业务状态，并处理与该状态相关的业务逻辑。
- **【强制】** BLoC必须通过依赖注入（DI）从数据仓库（Repository）获取数据，以保持与数据层的解耦。

### 7.3 状态与事件定义
- **【强制】** 事件（Event）的命名应清晰描述用户意图或发生的动作，通常是动词短语。例如: `FetchUserData`, `SubmitForm`。
- **【强制】** 状态（State）的命名应描述UI的不同状态，通常是名词或形容词短语。**【强制】** 使用一个密封类（Sealed Class），并结合
`freezed` 包来实现，以表示状态。**禁止**使用枚举（Enum）来定义状
态，因为密封类可以携带数据，表达能力更强。例如: `UserInitial`,
`UserLoadInProgress`, `UserLoadSuccess`, `UserLoadFailure`。。
- **【强制】** 状态（State）必须使用 `freezed` 包结合密封类（Sealed Class）进行定义。

### 7.4 UI集成
- **【强制】** 使用 `BlocProvider` 在Widget树的上层提供BLoC实例。
- **【强制】** 使用 `BlocBuilder`、`BlocListener` 或 `BlocConsumer` 响应状态变化并构建UI。
- **【严禁】** 在UI层直接创建BLoC实例，破坏了依赖注入原则。

## 8. 路由管理规范

**【强制】** 项目统一使用 **GoRouter** 进行路由管理。

### 8.1 路由配置
- **【强制】** 所有路由配置必须在 `src/routing/` 目录下集中管理。
- **`app_routes.dart`**: 定义所有路由的名称、路径等常量。
- **`app_router.dart`**: 创建并配置 `GoRouter` 实例，关联路径与页面。

### 8.2 路由跳转
- **【推荐】** 使用命名路由 (`context.goNamed('routeName')`) 进行跳转，而不是路径 (`context.go('/path')`)，以便于重构。
- **【强制】** 页面间传递参数必须通过路由参数进行，而不是依赖全局变量或单例。

## 9. 网络与数据处理规范

### 9.1 网络请求
- **【强制】** 使用 `dio` 作为网络请求库。
- **【强制】** 创建一个全局的 `Dio` 实例，并使用拦截器（`Interceptors`）统一处理日志记录、请求头添加（如Token）、错误处理和重试逻辑。

### 9.2 数据模型
- **【强制】** 对所有来自API的响应创建严格的数据模型。
- **【强制】** 使用 `freezed` 创建不可变的数据模型和状态类，以增强代码的健壮性。
- **【强制】** 结合 `json_serializable` 自动生成 `fromJson`/`toJson` 方法，避免手动解析JSON。

### 9.3 Repository模式
- **【强制】** 使用Repository（仓库）模式来隔离数据来源（网络、本地数据库、缓存）。
- **【强制】** UI层（通过BLoC）只能与Repository交互，不能直接调用API客户端或数据库。
- **【强制】** Repository的抽象接口必须定义在领域层（`domain/repositories`），其具体实现必须放在数据层（`data/repositories`）。这确保了业务逻辑（领域层）与数据来源（数据层）的完全解耦。

## 10. 错误与日志处理规范

### 10.1 错误处理
- **【强制】** 在Repository层捕获所有来自数据源的异常（网络、数据库等）。
- **【强制】** 所有Repository方法的返回类型 **必须** 使用 `multiple_result` 包进行封装，返回 `Result<Success, Error>` 类型。这使得错误处理在编译期即被强制检查，杜绝了遗漏错误处理的可能。
- **【强制】** BLoC层处理 `Result` 对象，并将错误状态（`State`）传递给UI层。
- **【强制】** UI层根据错误状态向用户显示友好、可理解的错误提示，如 `SnackBar` 或 `Dialog`。

### 10.2 日志记录
- **【强制】** 使用 `logger` 包进行日志记录，**严禁使用 `print()`**。
- **【强制】** 配置统一的Logger实例，根据不同环境（Debug/Release）设置不同的日志级别和输出格式。
- **【推荐】** 在关键节点记录日志，如：
    - 应用生命周期事件
    - BLoC的事件、过渡和错误
    - Dio拦截器中的请求和响应
    - 捕获到的异常

## 11. 测试规范

遵循 [Flutter测试指南](https://docs.flutter.dev/testing)。

### 11.1 测试类型
- **单元测试 (Unit Test)**: 测试单个函数、方法或类。主要用于测试 BLoC 和 Repository 中的业务逻辑。
- **组件测试 (Widget Test)**: 测试单个Widget，验证其UI和交互是否符合预期。
- **集成测试 (Integration Test)**: 测试完整的应用或大部分功能模块，模拟真实用户操作。

### 11.2 测试工具
- **【强制】** 统一使用 `mocktail` 来模拟依赖项。
- **【强制】** 测试文件必须以 `_test.dart` 结尾，并与被测试文件放在对应的 `test/` 目录下。

## 12. 依赖管理规范

### 12.1 `pubspec.yaml`
- **【强制】** 添加新依赖时，必须写明添加该依赖的理由作为注释。
- **【推荐】** 使用版本范围约束（例如 `^1.2.3`），而不是固定版本或 `any`。
- **【强制】** 定期（如每季度）运行 `flutter pub outdated` 检查并更新过时的依赖。

### 12.2 `pubspec.lock`
- **【强制】** `pubspec.lock` 文件必须提交到版本控制中，以确保团队成员和CI/CD环境使用完全相同的依赖版本。

## 13. Git工作流规范

本规范严格遵循项目组统一的 **Git工作流规范**。核心要点重申如下：

### 13.1 分支命名规范
- **功能开发**: `feature/功能描述` (e.g., `feature/user-login`)
- **Bug修复**: `bugfix/问题描述-或-ID` (e.g., `bugfix/login-button-crash`)
- **紧急修复**: `hotfix/紧急修复描述` (e.g., `hotfix/payment-api-error`)
- **发布准备**: `release/版本号` (e.g., `release/v1.2.0`)

### 13.2 提交消息规范
**【强制】** 遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范。

格式: `<type>(<scope>): <subject>`

- **`type`**: `feat` (新功能), `fix` (修复), `docs` (文档), `style` (格式), `refactor` (重构), `test`, `chore` (构建或杂项)。
- **`scope`** (可选): 本次提交影响的范围 (e.g., `auth`, `payment`, `ui`)。
- **`subject`**: 简短描述。

**示例:**
```
feat(auth): implement email and password login screen
```
fix(ui): correct alignment of the primary button on wide screens
```

---

## 14. 配置管理规范

### 14.1 多环境配置方案
**【强制】** 项目必须支持至少三种环境：开发 (dev)、测试 (test) 和生产 (prod)。
**【强制】** 使用 Flutter 的 `Flavors` 结合 `.env` 文件和 `flutter_dotenv` 包来管理不同环境的配置。

- **`.env.dev`**: 开发环境配置 (例如: `API_BASE_URL=https://api.dev.example.com`)
- **`.env.test`**: 测试环境配置
- **`.env.prod`**: 生产环境配置

### 14.2 启动应用
使用 `launch.json` (VS Code) 或 IDE 的配置来为不同 Flavor 定义启动命令。

**【强制】** 必须使用 `--dart-define-from-file` 标志加载对应的 `.env` 文件。

示例 `launch.json` 配置:
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run Dev",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "--flavor",
                "dev",
                "--dart-define-from-file",
                ".env.dev"
            ]
        },
        // ... test 和 prod 的配置
    ]
}
```

### 14.3 在代码中访问配置
**【强制】** 创建一个统一的 `AppConfig` 类来加载和提供配置变量，禁止在业务代码中直接访问 `dotenv`。

```dart
// 使用 flutter_dotenv 包
import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConfig {
  static Future<void> load() async {
    await dotenv.load();
  }

  static String get apiUrl => dotenv.env['API_BASE_URL'] ?? '';
}

// 在 main.dart 中初始化
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await AppConfig.load(); // 在应用启动前加载配置
  runApp(const MyApp());
}
```

## 15. 安全编码规范

### 15.1 敏感信息存储
- **【强制】** 严禁将 API 密钥、Token、加密密钥等敏感信息硬编码在代码中或存储在 `SharedPreferences` 中。
- **【强制】** 所有需要持久化的敏感信息（如用户Token、刷新Token）**必须**使用 `flutter_secure_storage` 包进行存储。该包利用了 Android 的 Keystore 和 iOS 的 Keychain。

### 15.2 网络安全
- **【强制】** 生产环境的 API 端点必须使用 HTTPS。
- **【强制】** 所有生产环境的API请求 **必须** 实施SSL证书锁定（Certificate Pinning）来防止中间人攻击。可以使用 `dio` 的 `HttpClientAdapter` 配合相关插件实现。

### 15.3 本地认证
- **【强制】** 所有需要保护本地敏感数据（例如用户个人信息、设置等）的功能，**必须** 使用 `local_auth` 包实现生物识别（指纹/面容ID）或设备密码认证。

### 15.4 防止代码逆向
- **【强制】** 针对 Android 平台，在构建 Release 版本时 **必须** 开启 ProGuard/R8 进行代码混淆和压缩。在 `android/app/build.gradle` 中配置。

## 16. 国际化(i18n)规范

### 16.1 方案选型
- **【强制】** 项目统一使用 Flutter 官方推荐的 `intl` 和 `flutter_localizations` 包进行国际化。

### 16.2 实施流程
1.  **添加依赖**: 在 `pubspec.yaml` 中添加 `flutter_localizations` 和 `intl`。
2.  **配置文件**: 在项目根目录创建 `l10n.yaml` 文件，配置输入和输出目录。
    ```yaml
    arb-dir: lib/src/localization/l10n
    template-arb-file: app_en.arb
    output-localization-file: app_localizations.dart
    ```
3.  **资源文件**: 在 `lib/src/localization/l10n` 目录下创建 `.arb` (Application Resource Bundle) 文件，例如 `app_en.arb` (英语), `app_zh.arb` (中文)。
    ```json
    // app_en.arb
    {
      "helloWorld": "Hello World!",
      "@helloWorld": {
        "description": "A friendly greeting."
      }
    }
    ```
4.  **代码生成**: 运行 `flutter gen-l10n` 自动生成本地化代理类。
5.  **在应用中使用**:
    - 在 `MaterialApp` 中配置 `localizationsDelegates` 和 `supportedLocales`。
    - 在 Widget 中通过 `AppLocalizations.of(context)!.helloWorld` 来访问本地化字符串。

### 16.3 最佳实践
- **【强制】** UI中所有面向用户的文本都必须通过国际化框架获取，严禁硬编码字符串。
- **【强制】** 使用带有参数的字符串时，**必须** 在 `.arb` 文件中使用占位符。

## 17. CI/CD集成规范

### 17.1 GitHub Actions 配置示例
**【强制】** 所有项目 **必须** 配置 CI/CD 流水线，在代码推送到 `main` 或 `develop` 分支以及创建 Pull Request 时自动触发。
**【强制】** CI/CD流程中必须包含代码覆盖率报告步骤，并使用Codecov或类似服务进行追踪，确保核心业务逻辑的测试覆盖率不低于85%。

以下是一个基础的 GitHub Actions 工作流 (`.github/workflows/flutter-ci.yml`)，用于代码检查和测试：
```yaml
name: Flutter CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.13.0' # 指定Flutter版本
          channel: 'stable'
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .

      - name: Analyze project
        run: flutter analyze --fatal-infos # 任何 info 级别以上的问题都会导致失败

      - name: Run tests
        run: flutter test --coverage # 运行所有测试并生成覆盖率报告

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }} # 需要在仓库 Secrets 中配置 Codecov Token
          files: coverage/lcov.info
```