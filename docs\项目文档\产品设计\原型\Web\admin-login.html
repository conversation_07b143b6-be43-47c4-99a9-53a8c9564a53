<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 茂名市地质灾害预警平台</title>
    <link rel="stylesheet" href="../common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 基于V5融合优化风的登录页面样式 */
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }

        /* 背景网格效果 */
        .grid-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.03;
            background-image:
                linear-gradient(rgba(59, 130, 246, 0.5) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.5) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: 0;
        }

        .login-container {
            width: 100%;
            max-width: 380px;
            background: rgba(30, 41, 59, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
            overflow: hidden;
        }

        /* 顶部装饰条 */
        .header-accent {
            height: 4px;
            background: linear-gradient(90deg, #1e40af 0%, #3b82f6 50%, #f59e0b 100%);
        }

        .login-header {
            padding: 24px 24px 16px;
            text-align: center;
            position: relative;
        }

        .main-logo {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            position: relative;
            box-shadow: 0 8px 32px rgba(30, 64, 175, 0.4);
        }

        .main-logo::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #f59e0b, #3b82f6, #f59e0b);
            border-radius: 16px;
            z-index: -1;
            animation: border-glow 3s ease-in-out infinite;
        }

        @keyframes border-glow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .main-logo i {
            font-size: 24px;
            color: white;
        }

        .system-title {
            color: #f1f5f9;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 6px;
        }

        .system-subtitle {
            color: #94a3b8;
            font-size: 14px;
        }

        .login-content {
            padding: 0 24px 24px;
        }

        .auth-notice {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .notice-icon {
            color: #fca5a5;
            font-size: 16px;
        }

        .notice-text {
            color: #fca5a5;
            font-size: 13px;
            line-height: 1.4;
        }

        .form-group {
            margin-bottom: 18px;
        }

        .form-label {
            display: block;
            color: #cbd5e1;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .input-wrapper {
            position: relative;
        }

        .form-input {
            width: 100%;
            padding: 14px 20px 14px 48px;
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 8px;
            color: #f1f5f9;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(15, 23, 42, 1);
        }

        .form-input::placeholder {
            color: #64748b;
        }

        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 16px;
            transition: color 0.3s ease;
        }

        .form-input:focus + .input-icon {
            color: #3b82f6;
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            cursor: pointer;
            font-size: 16px;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #3b82f6;
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
        }

        .remember-option {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox {
            width: 16px;
            height: 16px;
            accent-color: #3b82f6;
            margin: 0;
            vertical-align: middle;
        }

        .checkbox-label {
            color: #94a3b8;
            font-size: 13px;
            line-height: 16px;
            vertical-align: middle;
        }

        .login-button {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
        }

        .login-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-button:hover::before {
            left: 100%;
        }

        .login-button:disabled {
            background: #64748b;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .security-footer {
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid rgba(71, 85, 105, 0.3);
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .security-item {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #64748b;
            font-size: 11px;
        }

        .security-icon {
            color: #10b981;
            font-size: 10px;
        }

        .copyright-footer {
            margin-top: 16px;
            padding-top: 12px;
            border-top: 1px solid rgba(71, 85, 105, 0.2);
            text-align: center;
            color: #64748b;
            font-size: 11px;
            line-height: 1.4;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 响应式设计 - 优化低分辨率和移动设备 */
        @media (max-height: 700px) {
            body {
                padding: 12px;
            }

            .login-container {
                max-width: 360px;
            }

            .login-header {
                padding: 20px 24px 12px;
            }

            .main-logo {
                width: 48px;
                height: 48px;
                margin-bottom: 12px;
            }

            .main-logo i {
                font-size: 20px;
            }

            .system-title {
                font-size: 16px;
            }

            .system-subtitle {
                font-size: 13px;
            }

            .login-content {
                padding: 0 20px 20px;
            }

            .form-group {
                margin-bottom: 16px;
            }

            .auth-notice {
                padding: 10px 12px;
                margin-bottom: 16px;
            }

            .login-options {
                margin: 16px 0;
            }

            .security-footer {
                margin-top: 16px;
                padding-top: 12px;
                gap: 12px;
                flex-wrap: wrap;
            }

            .copyright-footer {
                margin-top: 12px;
                padding-top: 8px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 8px;
            }

            .login-container {
                margin: 0;
            }

            .login-header,
            .login-content {
                padding-left: 16px;
                padding-right: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="grid-background"></div>

    <div class="login-container">
        <div class="header-accent"></div>

        <div class="login-header">
            <div class="main-logo">
                <i class="fas fa-mountain"></i>
            </div>

            <div class="system-title">茂名市地质灾害预警平台</div>
            <div class="system-subtitle">茂名市自然资源局</div>
        </div>

        <div class="login-content">
            <div class="auth-notice">
                <i class="fas fa-exclamation-triangle notice-icon"></i>
                <div class="notice-text">
                    仅限授权用户访问，请确保在安全环境下登录
                </div>
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <label class="form-label">管理员账号</label>
                    <div class="input-wrapper">
                        <input type="text" class="form-input" id="username" placeholder="请输入管理员账号" required>
                        <i class="fas fa-user-shield input-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">登录密码</label>
                    <div class="input-wrapper">
                        <input type="password" class="form-input" id="password" placeholder="请输入登录密码" required>
                        <i class="fas fa-lock input-icon"></i>
                        <i class="fas fa-eye password-toggle" id="passwordToggle"></i>
                    </div>
                </div>

                <div class="login-options">
                    <label class="remember-option">
                        <input type="checkbox" class="checkbox" id="remember">
                        <span class="checkbox-label">记住登录</span>
                    </label>
                </div>

                <button type="submit" class="login-button" id="loginButton">
                    <i class="fas fa-sign-in-alt" style="margin-right: 8px;"></i>
                    <span id="loginText">安全登录</span>
                    <span id="loginLoading" class="loading" style="display: none;"></span>
                </button>
            </form>

            <div class="security-footer">
                <div class="security-item">
                    <i class="fas fa-check security-icon"></i>
                    <span>SSL加密</span>
                </div>
                <div class="security-item">
                    <i class="fas fa-check security-icon"></i>
                    <span>审计日志</span>
                </div>
                <div class="security-item">
                    <i class="fas fa-check security-icon"></i>
                    <span>会话保护</span>
                </div>
            </div>

            <div class="copyright-footer">
                <div>© 2025 茂名市自然资源局 版权所有</div>
                <div>请使用授权账户登录系统</div>
            </div>
        </div>
    </div>

    <script>
        // 密码显示/隐藏切换
        document.getElementById('passwordToggle').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this;

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // 表单提交
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginButton = document.getElementById('loginButton');
            const loginText = document.getElementById('loginText');
            const loginLoading = document.getElementById('loginLoading');

            // 显示加载状态
            loginButton.disabled = true;
            loginText.style.display = 'none';
            loginLoading.style.display = 'inline-block';

            // 模拟登录请求
            setTimeout(() => {
                // 简单的验证逻辑（实际项目中应该调用后端API）
                if (username && password) {
                    // 登录成功
                    loginButton.innerHTML = '<i class="fas fa-check" style="margin-right: 8px;"></i>验证成功';

                    setTimeout(() => {
                        window.location.href = 'admin-dashboard.html';
                    }, 1000);
                } else {
                    // 登录失败
                    loginButton.disabled = false;
                    loginButton.innerHTML = '<i class="fas fa-sign-in-alt" style="margin-right: 8px;"></i>安全登录';

                    // 简单的错误提示
                    alert('请输入用户名和密码');
                }
            }, 2000);
        });

        // 输入框焦点效果
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.nextElementSibling.style.color = '#3b82f6';
            });

            input.addEventListener('blur', function() {
                this.nextElementSibling.style.color = '#64748b';
            });
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动聚焦到用户名输入框
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
