<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 茂名市地质灾害预警平台</title>
    <link rel="stylesheet" href="../common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 基于M002结构的用户管理界面 - 与仪表板保持一致的设计风格 */

        /* 重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #0f172a;
            color: #e2e8f0;
            min-height: 100vh;
            overflow-x: hidden;
            line-height: 1.6;
        }

        .dashboard-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧导航栏 - 与M002完全一致，增加展开/收起功能 */
        .sidebar {
            width: 240px;
            background: #1e293b;
            border-right: 1px solid #475569;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
            z-index: 100;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
            transition: width 0.3s ease;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            border-bottom: 1px solid #475569;
            display: flex;
            align-items: center;
            gap: 12px;
            background: #1e293b;
            position: relative;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 0 14px;
            justify-content: center;
        }

        .sidebar-logo {
            width: 32px;
            height: 32px;
            background: #3b82f6;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .sidebar-title {
            color: #f1f5f9;
            font-size: 16px;
            font-weight: 600;
            white-space: nowrap;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .sidebar-title {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }



        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-group {
            margin-bottom: 24px;
        }

        .nav-group-title {
            color: #94a3b8;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 20px;
            margin-bottom: 8px;
            white-space: nowrap;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .nav-group-title {
            opacity: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.15s ease;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            border-radius: 0;
            position: relative;
        }

        .sidebar.collapsed .nav-item {
            padding: 10px 14px;
            justify-content: center;
        }

        .nav-item:hover {
            background: #334155;
            color: #f1f5f9;
        }

        .nav-item:active {
            background: #475569;
            transform: scale(0.98);
        }

        .nav-item.active {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border-right: 3px solid #3b82f6;
            font-weight: 600;
        }

        .nav-icon {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 14px;
            flex-shrink: 0;
        }

        .sidebar.collapsed .nav-icon {
            margin-right: 0;
        }

        .nav-text {
            white-space: nowrap;
            opacity: 1;
            transition: opacity 0.3s ease;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar.collapsed .nav-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        /* 收起状态下的提示 */
        .sidebar.collapsed .nav-item:hover::after {
            content: attr(data-title);
            position: absolute;
            left: 60px;
            top: 50%;
            transform: translateY(-50%);
            background: #1e293b;
            color: #f1f5f9;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            white-space: nowrap;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border: 1px solid #475569;
            z-index: 1000;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            background: #0f172a;
            transition: margin-left 0.3s ease;
        }

        .sidebar.collapsed + .main-content {
            margin-left: 60px;
        }

        /* 汉堡菜单按钮 - 桌面端和移动端通用 */
        .menu-toggle-btn {
            display: flex;
            width: 36px;
            height: 36px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            align-items: center;
            justify-content: center;
            color: #cbd5e1;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .menu-toggle-btn:hover {
            background: #475569;
            color: #f1f5f9;
        }

        .menu-toggle-btn:active {
            background: #64748b;
            transform: scale(0.95);
        }

        /* 移动端遮罩层 */
        .mobile-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 99;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .mobile-overlay.show {
            display: block;
            opacity: 1;
        }

        /* 顶部工具栏 - 与M002完全一致 */
        .top-bar {
            height: 64px;
            background: #1e293b;
            border-bottom: 1px solid #475569;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            position: sticky;
            top: 0;
            z-index: 50;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #94a3b8;
            font-size: 14px;
            font-weight: 500;
        }

        .breadcrumb-current {
            color: #f1f5f9;
            font-weight: 600;
        }

        .breadcrumb-separator {
            color: #64748b;
        }

        .top-bar-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .notification-btn {
            position: relative;
            width: 36px;
            height: 36px;
            border-radius: 6px;
            background: #334155;
            border: 1px solid #475569;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #cbd5e1;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .notification-btn:hover {
            background: #475569;
            border-color: #64748b;
            color: #f1f5f9;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            background: #ef4444;
            border: 2px solid #1e293b;
            border-radius: 50%;
            font-size: 10px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .user-menu:hover {
            background: #475569;
            border-color: #64748b;
        }

        .user-avatar {
            width: 24px;
            height: 24px;
            background: #3b82f6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .user-name {
            color: #f1f5f9;
            font-size: 14px;
            font-weight: 500;
        }

        /* 页面内容区 */
        .page-content {
            padding: 24px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .page-title {
            color: #f1f5f9;
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-title-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        /* 操作按钮区 */
        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.15s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #374151;
            color: #e5e7eb;
            border: 1px solid #4b5563;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        /* 搜索和筛选区 */
        .filter-section {
            background: #1e293b;
            border: 1px solid #475569;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }

        .filter-row {
            display: flex;
            gap: 16px;
            align-items: end;
        }

        .form-group {
            flex: 1;
        }

        .form-label {
            display: block;
            color: #cbd5e1;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 6px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            color: #f1f5f9;
            font-size: 14px;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 8px 12px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            color: #f1f5f9;
            font-size: 14px;
        }

        /* 用户表格 */
        .table-container {
            background: #1e293b;
            border: 1px solid #475569;
            border-radius: 8px;
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #334155;
            color: #f1f5f9;
            font-weight: 600;
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #475569;
            font-size: 14px;
        }

        .table td {
            padding: 12px 16px;
            border-bottom: 1px solid #475569;
            color: #cbd5e1;
            font-size: 14px;
        }

        .table tbody tr:hover {
            background: rgba(59, 130, 246, 0.05);
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }

        .status-inactive {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            border-radius: 4px;
            border: none;
            font-size: 12px;
            cursor: pointer;
            margin-right: 4px;
            transition: all 0.15s ease;
        }

        .btn-view {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }

        .btn-view:hover {
            background: rgba(59, 130, 246, 0.3);
        }

        .btn-edit {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }

        .btn-edit:hover {
            background: rgba(245, 158, 11, 0.3);
        }

        .btn-delete {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        .btn-delete:hover {
            background: rgba(239, 68, 68, 0.3);
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: #1e293b;
            border-top: 1px solid #475569;
        }

        .pagination-info {
            color: #94a3b8;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .pagination-btn {
            padding: 6px 12px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #cbd5e1;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .pagination-btn:hover {
            background: #475569;
        }

        .pagination-btn.active {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
        }

        /* Toast通知系统 */
        .toast-container {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 8px;
            max-width: 400px;
        }

        .toast {
            background: #1e293b;
            border: 1px solid #475569;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            min-width: 320px;
        }

        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast.hide {
            transform: translateX(100%);
            opacity: 0;
        }

        .toast-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .toast-content {
            flex: 1;
            min-width: 0;
        }

        .toast-title {
            color: #f1f5f9;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .toast-message {
            color: #cbd5e1;
            font-size: 13px;
            line-height: 1.4;
        }

        .toast-close {
            width: 20px;
            height: 20px;
            background: none;
            border: none;
            color: #64748b;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.15s ease;
            flex-shrink: 0;
        }

        .toast-close:hover {
            background: #374151;
            color: #cbd5e1;
        }

        /* Toast类型样式 */
        .toast.success {
            border-left: 4px solid #10b981;
        }

        .toast.success .toast-icon {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }

        .toast.error {
            border-left: 4px solid #ef4444;
        }

        .toast.error .toast-icon {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        .toast.warning {
            border-left: 4px solid #f59e0b;
        }

        .toast.warning .toast-icon {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }

        .toast.info {
            border-left: 4px solid #3b82f6;
        }

        .toast.info .toast-icon {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }

        /* 抽屉组件 */
        .drawer-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 200;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .drawer-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .drawer {
            position: fixed;
            top: 0;
            right: 0;
            width: 500px;
            height: 100vh;
            background: #1e293b;
            border-left: 1px solid #475569;
            z-index: 201;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
            box-shadow: -4px 0 15px rgba(0, 0, 0, 0.3);
        }

        .drawer.show {
            transform: translateX(0);
        }

        .drawer-header {
            height: 64px;
            padding: 0 24px;
            border-bottom: 1px solid #475569;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #1e293b;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .drawer-title {
            color: #f1f5f9;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .drawer-close {
            width: 32px;
            height: 32px;
            background: #374151;
            border: 1px solid #4b5563;
            border-radius: 6px;
            color: #9ca3af;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s ease;
        }

        .drawer-close:hover {
            background: #4b5563;
            color: #f3f4f6;
        }

        .drawer-body {
            padding: 24px;
        }

        .drawer-footer {
            padding: 16px 24px;
            border-top: 1px solid #475569;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            background: #1e293b;
            position: sticky;
            bottom: 0;
        }

        /* 确认对话框 */
        .confirm-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 300;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .confirm-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .confirm-dialog {
            background: #1e293b;
            border: 1px solid #475569;
            border-radius: 12px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .confirm-overlay.show .confirm-dialog {
            transform: scale(1);
        }

        .confirm-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .confirm-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        .confirm-title {
            color: #f1f5f9;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .confirm-message {
            color: #cbd5e1;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .confirm-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 加载状态 */
        .btn.loading {
            position: relative;
            color: transparent !important;
            pointer-events: none;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 16px;
            height: 16px;
            margin: -8px 0 0 -8px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            color: inherit;
        }

        .btn-primary.loading::after {
            border-top-color: white;
        }

        .btn-secondary.loading::after {
            border-top-color: #d1d5db;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 表单行布局 */
        .form-row {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }

        .form-row .form-group {
            flex: 1;
            min-width: 0;
        }

        .form-group.full-width {
            flex: 1 1 100%;
            width: 100%;
        }

        /* 跨列表单行 */
        .form-row.full-width-row {
            flex-direction: column !important;
        }

        .form-row.full-width-row .form-group {
            flex: none !important;
            width: 100% !important;
            min-width: 100% !important;
        }

        .form-row.full-width-row .form-textarea {
            width: 100% !important;
            box-sizing: border-box;
        }

        .form-textarea {
            padding: 8px 12px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #f1f5f9;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-textarea::placeholder {
            color: #64748b;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 60px;
            }

            .sidebar-title,
            .nav-group-title,
            .nav-text {
                opacity: 0;
                width: 0;
                overflow: hidden;
            }

            .sidebar-header {
                padding: 0 14px;
                justify-content: center;
            }

            .nav-item {
                padding: 10px 14px;
                justify-content: center;
            }

            .nav-icon {
                margin-right: 0;
            }

            .main-content {
                margin-left: 60px;
            }
        }

        @media (max-width: 768px) {
            /* 移动端样式保持不变 */

            /* 抽屉适配 */
            .drawer {
                width: 90%;
                max-width: 450px;
            }

            /* Toast适配 */
            .toast-container {
                right: 10px;
                left: 10px;
                max-width: none;
                top: 70px;
            }

            .toast {
                min-width: auto;
            }

            /* 确认对话框适配 */
            .confirm-dialog {
                margin: 20px;
                max-width: calc(100% - 40px);
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 240px;
                z-index: 100;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .sidebar.collapsed {
                width: 240px;
            }

            .sidebar.open .sidebar-title,
            .sidebar.open .nav-group-title,
            .sidebar.open .nav-text {
                opacity: 1;
                width: auto;
            }

            .sidebar.open .sidebar-header {
                padding: 0 20px;
                justify-content: flex-start;
            }

            .sidebar.open .nav-item {
                padding: 10px 20px;
                justify-content: flex-start;
            }

            .sidebar.open .nav-icon {
                margin-right: 12px;
            }

            .main-content {
                margin-left: 0;
                width: 100%;
            }

            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }

            .action-buttons {
                flex-direction: column;
            }

            .table-container {
                overflow-x: auto;
            }

            .page-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
            }

            .breadcrumb {
                display: none;
            }

            /* 确保移动端没有空白 */
            body {
                overflow-x: hidden;
            }

            .dashboard-layout {
                width: 100%;
                overflow-x: hidden;
            }
        }

        @media (max-width: 480px) {
            .page-content {
                padding: 16px;
            }

            .filter-section {
                padding: 16px;
            }

            .table th,
            .table td {
                padding: 8px 12px;
                font-size: 12px;
            }

            .action-btn {
                padding: 6px;
                margin-right: 2px;
            }

            .pagination {
                flex-direction: column;
                gap: 12px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Toast通知容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- 确认对话框 -->
    <div class="confirm-overlay" id="confirmOverlay">
        <div class="confirm-dialog">
            <div class="confirm-header">
                <div class="confirm-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="confirm-title" id="confirmTitle">确认操作</h3>
            </div>
            <div class="confirm-message" id="confirmMessage">
                您确定要执行此操作吗？
            </div>
            <div class="confirm-actions">
                <button class="btn btn-secondary" onclick="hideConfirm()">取消</button>
                <button class="btn btn-delete" id="confirmBtn">确认</button>
            </div>
        </div>
    </div>

    <!-- 移动端遮罩层 -->
    <div class="mobile-overlay" onclick="closeMobileSidebar()"></div>

    <!-- 抽屉遮罩层 -->
    <div class="drawer-overlay" onclick="closeDrawer()"></div>

    <div class="dashboard-layout">
        <!-- 左侧导航栏 - 与M002完全一致的结构，增加展开/收起功能 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-mountain"></i>
                </div>
                <div class="sidebar-title">地质灾害预警</div>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-group">
                    <div class="nav-group-title">主要功能</div>
                    <a href="admin-dashboard.html" class="nav-item" data-title="系统仪表板">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">系统仪表板</span>
                    </a>
                    <a href="#" class="nav-item" data-title="监测管理">
                        <i class="fas fa-map-marked-alt nav-icon"></i>
                        <span class="nav-text">监测管理</span>
                    </a>
                    <a href="admin-warning.html" class="nav-item" data-title="预警管理">
                        <i class="fas fa-exclamation-triangle nav-icon"></i>
                        <span class="nav-text">预警管理</span>
                    </a>
                    <a href="admin-data-management.html" class="nav-item" data-title="数据管理">
                        <i class="fas fa-database nav-icon"></i>
                        <span class="nav-text">数据管理</span>
                    </a>
                </div>

                <div class="nav-group">
                    <div class="nav-group-title">系统管理</div>
                    <a href="#" class="nav-item active" data-title="用户管理">
                        <i class="fas fa-users nav-icon"></i>
                        <span class="nav-text">用户管理</span>
                    </a>
                    <a href="#" class="nav-item" data-title="系统设置">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">系统设置</span>
                    </a>
                    <a href="#" class="nav-item" data-title="统计报告">
                        <i class="fas fa-chart-bar nav-icon"></i>
                        <span class="nav-text">统计报告</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部工具栏 - 与M002完全一致的结构，增加移动端菜单 -->
            <div class="top-bar">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <button class="menu-toggle-btn" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="breadcrumb">
                        <span>首页</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-current">用户管理</span>
                    </div>
                </div>

                <div class="top-bar-actions">
                    <div class="notification-btn" title="通知">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">3</div>
                    </div>

                    <div class="user-menu">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="user-name">管理员</span>
                        <i class="fas fa-chevron-down" style="font-size: 10px; color: #64748b;"></i>
                    </div>
                </div>
            </div>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <div class="page-title-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        用户管理
                    </h1>
                    <div class="action-buttons">
                        <button class="btn btn-secondary" onclick="exportUsers()">
                            <i class="fas fa-download"></i>
                            导出用户
                        </button>
                        <button class="btn btn-primary" onclick="openAddUserDrawer()">
                            <i class="fas fa-plus"></i>
                            新增用户
                        </button>
                    </div>
                </div>

                <!-- 搜索筛选区 -->
                <div class="filter-section">
                    <div class="filter-row">
                        <div class="form-group">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-input" placeholder="请输入用户名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">姓名</label>
                            <input type="text" class="form-input" placeholder="请输入姓名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">角色</label>
                            <select class="form-select">
                                <option value="">全部角色</option>
                                <option value="admin">系统管理员</option>
                                <option value="operator">操作员</option>
                                <option value="viewer">查看员</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态</label>
                            <select class="form-select">
                                <option value="">全部状态</option>
                                <option value="active">启用</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                        <div class="action-buttons">
                            <button class="btn btn-primary" onclick="searchUsers()">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                            <button class="btn btn-secondary" onclick="resetSearch()">
                                <i class="fas fa-redo"></i>
                                重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 用户表格 -->
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" style="margin-right: 8px;">
                                    用户名
                                </th>
                                <th>姓名</th>
                                <th>邮箱</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>最后登录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <input type="checkbox" style="margin-right: 8px;">
                                    admin
                                </td>
                                <td>系统管理员</td>
                                <td><EMAIL></td>
                                <td>系统管理员</td>
                                <td><span class="status-badge status-active">启用</span></td>
                                <td>2025-01-01 10:00</td>
                                <td>2025-07-13 09:30</td>
                                <td>
                                    <button class="action-btn btn-view" title="查看" onclick="viewUser('admin')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn btn-edit" title="编辑" onclick="editUser('admin')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn btn-delete" title="删除" onclick="deleteUser('admin', '系统管理员')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox" style="margin-right: 8px;">
                                    operator01
                                </td>
                                <td>张三</td>
                                <td><EMAIL></td>
                                <td>操作员</td>
                                <td><span class="status-badge status-active">启用</span></td>
                                <td>2025-02-15 14:20</td>
                                <td>2025-07-12 16:45</td>
                                <td>
                                    <button class="action-btn btn-view" title="查看" onclick="viewUser('operator01')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn btn-edit" title="编辑" onclick="editUser('operator01')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn btn-delete" title="删除" onclick="deleteUser('operator01', '张三')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox" style="margin-right: 8px;">
                                    viewer01
                                </td>
                                <td>李四</td>
                                <td><EMAIL></td>
                                <td>查看员</td>
                                <td><span class="status-badge status-inactive">禁用</span></td>
                                <td>2025-03-10 09:15</td>
                                <td>2025-07-10 11:20</td>
                                <td>
                                    <button class="action-btn btn-view" title="查看" onclick="viewUser('viewer01')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn btn-edit" title="编辑" onclick="editUser('viewer01')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn btn-delete" title="删除" onclick="deleteUser('viewer01', '李四')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox" style="margin-right: 8px;">
                                    operator02
                                </td>
                                <td>王五</td>
                                <td><EMAIL></td>
                                <td>操作员</td>
                                <td><span class="status-badge status-active">启用</span></td>
                                <td>2025-04-20 11:30</td>
                                <td>2025-07-11 14:15</td>
                                <td>
                                    <button class="action-btn btn-view" title="查看" onclick="viewUser('operator02')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn btn-edit" title="编辑" onclick="editUser('operator02')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn btn-delete" title="删除" onclick="deleteUser('operator02', '王五')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox" style="margin-right: 8px;">
                                    viewer02
                                </td>
                                <td>赵六</td>
                                <td><EMAIL></td>
                                <td>查看员</td>
                                <td><span class="status-badge status-active">启用</span></td>
                                <td>2025-05-08 16:45</td>
                                <td>2025-07-09 10:30</td>
                                <td>
                                    <button class="action-btn btn-view" title="查看" onclick="viewUser('viewer02')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn btn-edit" title="编辑" onclick="editUser('viewer02')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn btn-delete" title="删除" onclick="deleteUser('viewer02', '赵六')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            显示 1-5 条，共 5 条记录
                        </div>
                        <div class="pagination-controls">
                            <button class="pagination-btn" disabled>上一页</button>
                            <button class="pagination-btn active">1</button>
                            <button class="pagination-btn" disabled>下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧抽屉 -->
    <div class="drawer" id="userDrawer">
        <div class="drawer-header">
            <h3 class="drawer-title" id="drawerTitle">
                <i class="fas fa-user-plus"></i>
                新增用户
            </h3>
            <button class="drawer-close" onclick="closeDrawer()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="drawer-body" id="drawerBody">
            <!-- 动态内容将在这里加载 -->
        </div>
        <div class="drawer-footer" id="drawerFooter">
            <!-- 动态按钮将在这里加载 -->
        </div>
    </div>

    <script>
        // 全局变量
        let currentEditUserId = null;

        // Toast通知系统
        class ToastManager {
            constructor() {
                this.container = document.getElementById('toastContainer');
                this.toasts = [];
            }

            show(type, title, message, duration = 4000) {
                const toast = this.createToast(type, title, message);
                this.container.appendChild(toast);
                this.toasts.push(toast);

                // 触发显示动画
                setTimeout(() => {
                    toast.classList.add('show');
                }, 10);

                // 自动隐藏
                setTimeout(() => {
                    this.hide(toast);
                }, duration);

                return toast;
            }

            createToast(type, title, message) {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;

                const iconMap = {
                    success: 'fas fa-check',
                    error: 'fas fa-times',
                    warning: 'fas fa-exclamation',
                    info: 'fas fa-info'
                };

                toast.innerHTML = `
                    <div class="toast-icon">
                        <i class="${iconMap[type]}"></i>
                    </div>
                    <div class="toast-content">
                        <div class="toast-title">${title}</div>
                        <div class="toast-message">${message}</div>
                    </div>
                    <button class="toast-close" onclick="toastManager.hide(this.parentElement)">
                        <i class="fas fa-times"></i>
                    </button>
                `;

                return toast;
            }

            hide(toast) {
                toast.classList.add('hide');
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.parentElement.removeChild(toast);
                    }
                    const index = this.toasts.indexOf(toast);
                    if (index > -1) {
                        this.toasts.splice(index, 1);
                    }
                }, 300);
            }

            success(title, message) {
                return this.show('success', title, message);
            }

            error(title, message) {
                return this.show('error', title, message);
            }

            warning(title, message) {
                return this.show('warning', title, message);
            }

            info(title, message) {
                return this.show('info', title, message);
            }
        }

        // 确认对话框系统
        class ConfirmManager {
            constructor() {
                this.overlay = document.getElementById('confirmOverlay');
                this.title = document.getElementById('confirmTitle');
                this.message = document.getElementById('confirmMessage');
                this.confirmBtn = document.getElementById('confirmBtn');
                this.currentResolve = null;
            }

            show(title, message, confirmText = '确认') {
                return new Promise((resolve) => {
                    this.currentResolve = resolve;
                    this.title.textContent = title;
                    this.message.textContent = message;
                    this.confirmBtn.textContent = confirmText;

                    this.confirmBtn.onclick = () => {
                        this.overlay.classList.remove('show');
                        this.currentResolve = null;
                        resolve(true);
                    };

                    this.overlay.classList.add('show');
                });
            }

            hide() {
                this.overlay.classList.remove('show');
                if (this.currentResolve) {
                    this.currentResolve(false);
                    this.currentResolve = null;
                }
            }
        }

        // 初始化管理器
        const toastManager = new ToastManager();
        const confirmManager = new ConfirmManager();

        // 隐藏确认对话框
        function hideConfirm() {
            confirmManager.hide();
        }

        // 全选/反选功能
        document.addEventListener('DOMContentLoaded', function() {
            const headerCheckbox = document.querySelector('thead input[type="checkbox"]');
            const rowCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');

            if (headerCheckbox && rowCheckboxes.length > 0) {
                headerCheckbox.addEventListener('change', function() {
                    rowCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });

                rowCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const checkedCount = document.querySelectorAll('tbody input[type="checkbox"]:checked').length;
                        headerCheckbox.checked = checkedCount === rowCheckboxes.length;
                        headerCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
                    });
                });
            }
        });

        // 统一的侧边栏切换功能 - 桌面端和移动端通用
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuIcon = document.querySelector('.menu-toggle-btn i');

            // 移动端：显示/隐藏侧边栏
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('open');

                if (sidebar.classList.contains('open')) {
                    overlay.classList.add('show');
                    document.body.style.overflow = 'hidden';
                    menuIcon.className = 'fas fa-times'; // 显示关闭图标
                } else {
                    overlay.classList.remove('show');
                    document.body.style.overflow = '';
                    menuIcon.className = 'fas fa-bars'; // 显示汉堡图标
                }
            }
            // 桌面端：展开/收起侧边栏
            else {
                sidebar.classList.toggle('collapsed');

                if (sidebar.classList.contains('collapsed')) {
                    menuIcon.className = 'fas fa-indent'; // 收起状态：显示展开图标
                } else {
                    menuIcon.className = 'fas fa-outdent'; // 展开状态：显示收起图标
                }
            }
        }

        // 关闭移动端侧边栏
        function closeMobileSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuIcon = document.querySelector('.menu-toggle-btn i');

            sidebar.classList.remove('open');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
            menuIcon.className = 'fas fa-bars'; // 恢复汉堡图标
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuIcon = document.querySelector('.menu-toggle-btn i');

            if (window.innerWidth > 768) {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }

            // 在平板尺寸自动收起侧边栏
            if (window.innerWidth <= 1024 && window.innerWidth > 768) {
                sidebar.classList.add('collapsed');
                menuIcon.className = 'fas fa-indent'; // 收起状态图标
            } else if (window.innerWidth > 1024) {
                sidebar.classList.remove('collapsed');
                menuIcon.className = 'fas fa-outdent'; // 展开状态图标
            }

            // 移动端恢复汉堡图标
            if (window.innerWidth <= 768) {
                menuIcon.className = 'fas fa-bars';
            }
        });

        // 抽屉控制功能
        function openDrawer() {
            document.getElementById('userDrawer').classList.add('show');
            document.querySelector('.drawer-overlay').classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeDrawer() {
            document.getElementById('userDrawer').classList.remove('show');
            document.querySelector('.drawer-overlay').classList.remove('show');
            document.body.style.overflow = '';
            currentEditUserId = null;
        }

        // 用户管理功能
        function openAddUserDrawer() {
            currentEditUserId = null;
            document.getElementById('drawerTitle').innerHTML = '<i class="fas fa-user-plus"></i> 新增用户';

            const drawerBody = document.getElementById('drawerBody');
            drawerBody.innerHTML = `
                <form id="userForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">用户名 *</label>
                            <input type="text" class="form-input" id="username" placeholder="请输入用户名" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">姓名 *</label>
                            <input type="text" class="form-input" id="realName" placeholder="请输入姓名" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">邮箱 *</label>
                            <input type="email" class="form-input" id="email" placeholder="请输入邮箱地址" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">手机号</label>
                            <input type="tel" class="form-input" id="phone" placeholder="请输入手机号">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">角色 *</label>
                            <select class="form-select" id="role" required>
                                <option value="">请选择角色</option>
                                <option value="admin">系统管理员</option>
                                <option value="operator">操作员</option>
                                <option value="viewer">查看员</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态</label>
                            <select class="form-select" id="status">
                                <option value="active">启用</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">密码 *</label>
                            <input type="password" class="form-input" id="password" placeholder="请输入密码" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">确认密码 *</label>
                            <input type="password" class="form-input" id="confirmPassword" placeholder="请再次输入密码" required>
                        </div>
                    </div>
                    <div class="form-row full-width-row">
                        <div class="form-group full-width">
                            <label class="form-label">备注</label>
                            <textarea class="form-textarea" id="remark" placeholder="请输入备注信息"></textarea>
                        </div>
                    </div>
                </form>
            `;

            const drawerFooter = document.getElementById('drawerFooter');
            drawerFooter.innerHTML = `
                <button type="button" class="btn btn-secondary" onclick="closeDrawer()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveUser()">保存</button>
            `;

            openDrawer();
        }

        // 初始化响应式状态
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.querySelector('.sidebar');
            const menuIcon = document.querySelector('.menu-toggle-btn i');

            if (window.innerWidth <= 1024 && window.innerWidth > 768) {
                sidebar.classList.add('collapsed');
                menuIcon.className = 'fas fa-indent'; // 收起状态图标
            } else if (window.innerWidth > 1024) {
                menuIcon.className = 'fas fa-outdent'; // 展开状态图标
            } else {
                menuIcon.className = 'fas fa-bars'; // 移动端汉堡图标
            }

            // 导航项点击事件 - 与M002保持一致
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    // 如果有实际链接且不是#，则允许跳转
                    const href = this.getAttribute('href');
                    if (href && href !== '#' && href !== 'javascript:void(0)') {
                        // 允许正常跳转，不阻止默认行为
                        return;
                    }

                    // 只有空链接或#链接才阻止默认行为
                    e.preventDefault();

                    // 移除所有active状态
                    document.querySelectorAll('.nav-item').forEach(nav => {
                        nav.classList.remove('active');
                    });

                    // 添加active状态到当前项
                    this.classList.add('active');

                    // 更新面包屑
                    const itemText = this.querySelector('.nav-text').textContent.trim();
                    const breadcrumb = document.querySelector('.breadcrumb');
                    breadcrumb.innerHTML = `
                        <span>系统管理</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-current">${itemText}</span>
                    `;
                });
            });
        });

        // 编辑用户
        function editUser(userId) {
            currentEditUserId = userId;
            document.getElementById('drawerTitle').innerHTML = '<i class="fas fa-user-edit"></i> 编辑用户';

            const userData = getUserData(userId);

            const drawerBody = document.getElementById('drawerBody');
            drawerBody.innerHTML = `
                <form id="userForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">用户名 *</label>
                            <input type="text" class="form-input" id="username" value="${userData.username}" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">姓名 *</label>
                            <input type="text" class="form-input" id="realName" value="${userData.realName}" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">邮箱 *</label>
                            <input type="email" class="form-input" id="email" value="${userData.email}" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">手机号</label>
                            <input type="tel" class="form-input" id="phone" value="${userData.phone || ''}">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">角色 *</label>
                            <select class="form-select" id="role" required>
                                <option value="">请选择角色</option>
                                <option value="admin" ${userData.role === 'admin' ? 'selected' : ''}>系统管理员</option>
                                <option value="operator" ${userData.role === 'operator' ? 'selected' : ''}>操作员</option>
                                <option value="viewer" ${userData.role === 'viewer' ? 'selected' : ''}>查看员</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态</label>
                            <select class="form-select" id="status">
                                <option value="active" ${userData.status === 'active' ? 'selected' : ''}>启用</option>
                                <option value="inactive" ${userData.status === 'inactive' ? 'selected' : ''}>禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row full-width-row">
                        <div class="form-group full-width">
                            <label class="form-label">备注</label>
                            <textarea class="form-textarea" id="remark">${userData.remark || ''}</textarea>
                        </div>
                    </div>
                </form>
            `;

            const drawerFooter = document.getElementById('drawerFooter');
            drawerFooter.innerHTML = `
                <button type="button" class="btn btn-secondary" onclick="closeDrawer()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveUser()">保存</button>
            `;

            openDrawer();
        }

        // 查看用户
        function viewUser(userId) {
            const userData = getUserData(userId);
            document.getElementById('drawerTitle').innerHTML = '<i class="fas fa-user"></i> 用户详情';

            const drawerBody = document.getElementById('drawerBody');
            drawerBody.innerHTML = `
                <div style="background: #0f172a; border: 1px solid #475569; border-radius: 6px; padding: 16px; margin-bottom: 16px;">
                    <h4 style="color: #f1f5f9; margin-bottom: 12px; border-bottom: 1px solid #475569; padding-bottom: 8px;">基本信息</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 12px;">
                        <div><div style="color: #94a3b8; font-size: 12px; margin-bottom: 4px;">用户名</div><div style="color: #f1f5f9;">${userData.username}</div></div>
                        <div><div style="color: #94a3b8; font-size: 12px; margin-bottom: 4px;">姓名</div><div style="color: #f1f5f9;">${userData.realName}</div></div>
                        <div><div style="color: #94a3b8; font-size: 12px; margin-bottom: 4px;">邮箱</div><div style="color: #f1f5f9;">${userData.email}</div></div>
                        <div><div style="color: #94a3b8; font-size: 12px; margin-bottom: 4px;">角色</div><div style="color: #f1f5f9;">${userData.roleText}</div></div>
                        <div><div style="color: #94a3b8; font-size: 12px; margin-bottom: 4px;">状态</div><div><span class="status-badge ${userData.status === 'active' ? 'status-active' : 'status-inactive'}">${userData.status === 'active' ? '启用' : '禁用'}</span></div></div>
                    </div>
                </div>
                <div style="background: #0f172a; border: 1px solid #475569; border-radius: 6px; padding: 16px;">
                    <h4 style="color: #f1f5f9; margin-bottom: 12px; border-bottom: 1px solid #475569; padding-bottom: 8px;">备注信息</h4>
                    <div style="color: #f1f5f9;">${userData.remark || '无'}</div>
                </div>
            `;

            const drawerFooter = document.getElementById('drawerFooter');
            drawerFooter.innerHTML = `
                <button type="button" class="btn btn-secondary" onclick="closeDrawer()">关闭</button>
                <button type="button" class="btn btn-primary" onclick="editUser('${userId}')">编辑用户</button>
            `;

            openDrawer();
        }

        // 删除用户
        async function deleteUser(userId, userName) {
            const confirmed = await confirmManager.show(
                '确认删除',
                `确定要删除用户 "${userName}" 吗？\n\n删除后将无法恢复，请谨慎操作。`,
                '确认删除'
            );

            if (confirmed) {
                setTimeout(() => {
                    const deleteSuccess = Math.random() > 0.1;

                    if (deleteSuccess) {
                        toastManager.success('删除成功', `用户 "${userName}" 已成功删除`);
                    } else {
                        toastManager.error('删除失败', `删除用户 "${userName}" 时发生错误，请稍后重试`);
                    }
                }, 500);
            }
        }

        // 保存用户
        async function saveUser() {
            const form = document.getElementById('userForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (!currentEditUserId) {
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                if (password !== confirmPassword) {
                    toastManager.error('密码错误', '两次输入的密码不一致！');
                    return;
                }
            }

            const saveBtn = document.querySelector('.drawer-footer .btn-primary');
            saveBtn.classList.add('loading');
            saveBtn.disabled = true;

            setTimeout(() => {
                saveBtn.classList.remove('loading');
                saveBtn.disabled = false;

                if (currentEditUserId) {
                    toastManager.success('更新成功', '用户信息已成功更新');
                } else {
                    toastManager.success('创建成功', '用户已成功创建');
                }

                closeDrawer();
            }, 1500);
        }

        // 搜索和其他功能
        function searchUsers() {
            toastManager.info('搜索完成', '已根据条件筛选用户列表');
        }

        function resetSearch() {
            document.querySelectorAll('.filter-section input, .filter-section select').forEach(el => el.value = '');
            toastManager.info('重置完成', '搜索条件已重置');
        }

        function exportUsers() {
            toastManager.info('导出开始', '正在准备用户数据导出...');
            setTimeout(() => {
                toastManager.success('导出成功', '用户数据已成功导出到Excel文件');
            }, 2000);
        }

        // 模拟用户数据
        function getUserData(userId) {
            const users = {
                'admin': { username: 'admin', realName: '系统管理员', email: '<EMAIL>', phone: '138****8888', role: 'admin', roleText: '系统管理员', status: 'active', remark: '系统默认管理员账户' },
                'operator01': { username: 'operator01', realName: '张三', email: '<EMAIL>', phone: '139****9999', role: 'operator', roleText: '操作员', status: 'active', remark: '负责数据录入和日常操作' },
                'viewer01': { username: 'viewer01', realName: '李四', email: '<EMAIL>', phone: '137****7777', role: 'viewer', roleText: '查看员', status: 'inactive', remark: '临时查看权限，已停用' },
                'operator02': { username: 'operator02', realName: '王五', email: '<EMAIL>', phone: '136****6666', role: 'operator', roleText: '操作员', status: 'active', remark: '负责预警信息发布' },
                'viewer02': { username: 'viewer02', realName: '赵六', email: '<EMAIL>', phone: '135****5555', role: 'viewer', roleText: '查看员', status: 'active', remark: '外部合作单位查看权限' }
            };
            return users[userId] || {};
        }

        // ESC键支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                if (document.getElementById('userDrawer').classList.contains('show')) {
                    closeDrawer();
                } else if (document.getElementById('confirmOverlay').classList.contains('show')) {
                    hideConfirm();
                }
            }
        });
    </script>
</body>
</html>
