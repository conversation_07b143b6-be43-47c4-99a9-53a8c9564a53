---
type: "manual"
---

# Python 项目编码规范

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档名称 | Python 项目编码规范 |
| 文档版本 | v2.0.0 |
| 生效日期 | 2025-06-24 |
| 适用范围 | 所有Python项目 |
| 审批人 | 技术负责人 |
| 维护人 | 研发团队 |
| 更新频率 | 季度评审 |

## 目录

1. [概述和适用范围](#1-概述和适用范围)
2. [基础编码规范 (PEP 8)](#2-基础编码规范-pep-8)
3. [代码质量工具配置](#3-代码质量工具配置)
4. [文档和注释规范](#4-文档和注释规范)
5. [类型提示规范](#5-类型提示规范)
6. [项目结构和模块化](#6-项目结构和模块化)
7. [配置管理规范](#7-配置管理规范)
8. [日志记录规范](#8-日志记录规范)
9. [错误处理规范](#9-错误处理规范)
10. [性能优化指导](#10-性能优化指导)
11. [安全编码规范](#11-安全编码规范)
12. [Git工作流规范](#12-git工作流规范)
13. [代码审查规范](#13-代码审查规范)
14. [CI/CD集成规范](#14-cicd集成规范)
15. [一致性原则](#15-一致性原则)
16. [单元测试规范](#16-单元测试规范)
17. [检查清单](#17-检查清单)
18. [常见问题解答](#18-常见问题解答)
19. [违规处理流程](#19-违规处理流程)
20. [附录：配置文件示例](#20-附录配置文件示例)

## 1. 概述和适用范围

### 1.1 目标
本规范旨在为 Python 项目的代码提供一套统一的编码标准，以实现：
- 提高代码质量和可读性
- 增强代码可维护性
- 促进团队协作效率
- 减少代码审查时间
- 降低项目维护成本
- 提升开发效率

### 1.2 适用范围
- 所有新开发的Python项目
- 现有项目的重构和重要更新
- 第三方库和工具的集成
- 原型开发和生产环境代码

### 1.3 强制性要求
- **【强制】** 所有团队成员必须遵循本规范
- **【强制】** 所有代码提交前必须通过自动化检查
- **【强制】** 所有代码必须经过同行评审
- **【推荐】** 定期参加编码规范培训和分享

## 2. 基础编码规范 (PEP 8)

本项目严格遵循 [PEP 8 -- Style Guide for Python Code](https://www.python.org/dev/peps/pep-0008/)。关键点包括：

*   **命名规范:**
    *   函数、变量、方法名：使用小写字母和下划线 (`snake_case`)。
    *   类名：使用驼峰命名法 (`CamelCase`)。
    *   常量：使用全大写字母和下划线 (`ALL_CAPS`)。
    *   模块名：使用简短、全小写字母的名称，可以使用下划线。
    *   私有成员：以单下划线 `_` 开头。强烈不推荐使用双下划线 `__`（名称修饰）。  例如：`_age`。
*   **代码布局:**
    *   缩进：使用 4 个空格进行缩进，禁止使用 Tab。
    *   行长：每行代码长度限制在 88 个字符以内（与 black 格式化工具默认值一致）。对于长字符串或复杂表达式，可以使用括号或反斜杠进行换行。
    *   空行：
        *   顶级函数和类定义之间使用两个空行。
        *   类中的方法定义之间使用一个空行。
        *   函数内部可以使用空行来分隔逻辑块，但不要过度使用。
*   **导入 (Imports):**
    *   导入语句应放在文件顶部，位于模块文档字符串和注释之后，全局变量之前。
    *   导入应分组，顺序为：标准库导入、相关第三方库导入、本地应用程序/库特定导入。每组之间用一个空行分隔。
    *   推荐使用绝对导入。
    *   避免使用 `from module import *`。
*   **注释:**
    *   块注释：`#` 后跟一个空格，用于解释接下来的代码块。
    *   行内注释：至少在代码后留两个空格，然后是 `#` 和一个空格，用于解释单行代码。谨慎使用，代码本身应尽可能自明。
*   **空格:**
    *   在二元运算符（赋值、比较、布尔等）两边使用一个空格。
    *   在逗号、分号、冒号后使用一个空格，但不在其前使用。
    *   函数调用、索引、切片时，括号/方括号内侧不加空格。
    *   函数/方法定义的参数列表 `()` 内侧不加空格。
    *   赋值语句的 `=` 两边用空格，但函数参数默认值的 `=` 两边不用空格。

*   **工具推荐:** 使用 `flake8` 进行代码风格检查，使用 `black` 进行代码自动格式化, 使用`pytest`进行单元测试。

## 3. 代码质量工具配置

### 3.1 必需工具

#### 3.1.1 Black - 代码格式化
- **用途**: 自动代码格式化
- **配置**: 在 `pyproject.toml` 中配置
- **强制性**: 【强制】所有代码提交前必须经过 black 格式化

```toml
[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除的目录
  \.eggs
  | \.git
  | \.venv
  | build
  | dist
)/
'''
```

#### 3.1.2 Flake8 - 代码风格检查
- **用途**: PEP 8 合规性检查，代码质量检查
- **配置文件**: `.flake8` 或 `setup.cfg`

```ini
[flake8]
max-line-length = 88
max-complexity = 10
ignore =
    E203,  # 与 black 兼容
    W503,  # 与 black 兼容
exclude =
    .git,
    __pycache__,
    .venv,
    build,
    dist,
    *.egg-info
per-file-ignores =
    __init__.py:F401  # 允许在 __init__.py 中未使用的导入
```

#### 3.1.3 MyPy - 静态类型检查
- **用途**: 类型提示验证
- **强制性**: 【强制】所有公共API必须通过mypy检查

```toml
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
```

#### 3.1.4 isort - 导入排序
- **用途**: 自动排序和格式化导入语句

```toml
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = ["pytest", "numpy", "pandas"]
```

### 3.2 推荐工具

#### 3.2.1 Bandit - 安全检查
```bash
pip install bandit
bandit -r src/ -f json -o bandit-report.json
```

#### 3.2.2 Safety - 依赖安全检查
```bash
pip install safety
safety check --json --output safety-report.json
```

#### 3.2.3 Pre-commit - Git钩子
- **强制性**: 【强制】所有项目必须配置pre-commit

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
```

### 3.3 IDE配置建议

#### 3.3.1 VS Code
```json
// .vscode/settings.json
{
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

#### 3.3.2 PyCharm
- 启用 Black 插件
- 配置代码检查规则
- 设置自动导入排序

## 4. 文档和注释规范 (Docstrings - PEP 257 & Google Style)

遵循 [PEP 257 -- Docstring Conventions](https://www.python.org/dev/peps/pep-0257/)，并采用 **Google Python Style Docstrings** 格式。

*   所有公共模块、函数、类及其方法都必须包含文档字符串，文档必须使用中文描述。
*   文档字符串应以简短的单行摘要开始，然后是一个空行，接着是更详细的描述。
*   Google 风格的关键部分：
    *   `Args:`: 描述函数或方法的参数，格式为 `参数名 (类型): 描述`。
    *   `Returns:`: 描述函数或方法的返回值，格式为 `类型: 描述`。如果无返回值，则省略或写 `None`。
    *   `Raises:`: 描述可能抛出的异常，格式为 `异常类型: 抛出条件`。
    *   `Example:` (可选): 提供使用示例。

*   **模块级文档字符串规范**:
    - 必须位于文件顶部，在导入语句之前
    - 必须包含以下部分：
      1. 模块功能概述（单行）
      2. 详细功能描述
      3. 使用示例
      4. 注意事项
      5. 依赖项
      6. 作者信息
      7. 版本历史
      8. **代码更新同步**: 当修改、更新现有代码时，需要同步更新模块级文档中的版本和修改者信息。
    - 示例模板：
```python
"""模块功能概述。

详细功能描述:
    - 功能1描述
    - 功能2描述
    - 主要类和函数概述

使用示例:
    1. 基本用法示例
    2. 高级用法示例

注意事项:
    1. 使用限制
    2. 性能考虑
    3. 其他重要说明

依赖项:
    - 所需Python版本
    - 第三方库依赖
    - 系统依赖

作者:
    作者姓名 <邮箱>

版本:
    1.0.0 (YYYY-MM-DD): 初始版本
    1.1.0 (YYYY-MM-DD): 功能更新
"""
```

```python
import numpy as np

def calculate_area(width: float, height: float) -> float:
    """Calculates the area of a rectangle.

    Args:
        width (float): The width of the rectangle. Must be non-negative.
        height (float): The height of the rectangle. Must be non-negative.

    Returns:
        float: The calculated area of the rectangle.

    Raises:
        ValueError: If width or height is negative.
    """
    if width < 0 or height < 0:
        raise ValueError("Width and height must be non-negative.")
    return width * height
```

## 5. 类型提示规范 (Type Hinting - PEP 484)

强制要求使用类型提示，以提高代码清晰度和可维护性，并方便静态分析工具检查。

*   在函数和方法的签名中为所有参数和返回值添加类型注解。
*   使用 `typing` 模块来表示更复杂的类型，如 `List`, `Dict`, `Tuple`, `Optional`, `Union`, `Any` 等。
*   对于项目中常用到的复杂对象类型（例如，来自特定库的类，如 `pandas.DataFrame`），可以直接使用其类名作为类型提示，或使用 `typing.TypeAlias` 定义别名以简化。
*   变量注解（PEP 526）是可选的，但在复杂逻辑或需要明确类型时推荐使用。

```python
from typing import List, Tuple, Optional, TypeAlias
import pandas as pd
import numpy as np

# 示例：为常用复杂类型定义别名
ProcessedData: TypeAlias = pd.DataFrame
RawDataDict: TypeAlias = Dict[str, List[Union[int, float]]]

def process_complex_data(
    input_path: str,
    processed_data: Optional[ProcessedData] = None
) -> Tuple[np.ndarray, RawDataDict]:
    """处理复杂数据结构的示例函数。
    (注意：这里的 pandas 和 numpy 仅为示例，实际类型取决于项目。)
    """
    # ... implementation ...
    pass
```

## 6. 项目结构和模块化

*   代码应按功能逻辑划分到不同的模块 (`.py` 文件) 和包 (包含 `__init__.py` 的目录) 中。遵循项目结构中定义的目录。
*   遵循**高内聚、低耦合**原则：每个模块/类/函数应专注于单一职责，减少模块间的依赖。
*   **项目结构约定**:
    *   `src/`: 存放项目核心的、可重用的库代码（模块、类、函数）。**`src` 目录下的代码不应包含直接的执行逻辑或命令行入口点。**
    *   `scripts/`: 存放用于执行特定任务的脚本文件。这些脚本通常作为应用程序的入口点，负责解析命令行参数、加载配置、调用 `src` 中的库代码来完成任务。
    *   `tests/`: 存放单元测试和集成测试代码。
*   **逻辑分离**: 严格区分通用工具/基础功能与特定业务/项目流程。
    *   **通用逻辑**: 可在不同项目或场景下复用的功能（如通用的文件操作、数据转换、数学计算等）应放在 `src/utils/` 或类似的基础模块中。
    *   **特定逻辑**: 与本项目特定目标（如特定业务流程、特定数据模型处理、特定算法应用）紧密相关的代码应放在 `src/` 下相应的业务模块中（如 `src/module_a/`, `src/module_b/`）。
    *   **避免耦合**: 避免将通用逻辑与特定业务流程紧密耦合在同一个函数或类中。例如，通用的数据验证逻辑应独立实现，供处理特定业务数据的流程调用。
*   避免循环导入。

## 7. 配置管理规范

*   **严禁硬编码**：不要将文件路径、API密钥、模型超参数、训练设置等直接写入代码。
*   使用配置文件（如 `.yaml`, `.ini`, `.env` 或 Python 模块 `config.py`）来管理这些配置项。
*   配置文件应放在 `config/` 目录下。
*   **配置文件加载**:
    *   应提供统一的配置加载机制（例如，`src/config.py` 中的 `Config` 类）。
    *   **加载失败处理**: 必须明确配置文件的必要性。
        *   **必需配置文件**: 如果脚本运行绝对依赖某个配置文件，加载失败时（如文件未找到、格式错误），应记录错误日志并抛出明确的自定义异常（如 `ConfigError`），由调用方（通常是 `main` 函数或脚本入口）捕获并终止程序，返回非零退出码。
        *   **可选配置文件**: 如果配置文件是可选的（允许使用默认值），加载失败时应记录**警告**日志，并确保程序能继续使用预设的默认配置运行。
        *   **处理空文件**: 配置加载逻辑应能健壮地处理空配置文件的情况（例如，`yaml.safe_load` 返回 `None` 时，应视为空配置 `{}` 处理）。
*   考虑使用 `argparse` 或类似库处理命令行参数，增加脚本的灵活性。

## 8. 日志记录规范

*   使用 Python 内置的 `logging` 模块进行日志记录，**禁止使用 `print()`** 进行调试或信息输出。
*   配置合适的日志级别（`DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`）。开发时可使用 `DEBUG`，部署时调整为 `INFO` 或 `WARNING`。
*   配置日志格式，包含时间戳、日志级别、模块名、行号和消息内容。
*   将日志同时输出到控制台和文件（例如 `logs/app.log`），便于实时监控和事后追踪。
*   在关键操作（如数据加载、模型训练步骤、异常捕获）处添加日志记录。
*   **统一配置加载**: 所有脚本必须通过统一的机制加载日志配置。推荐定义一个专门的辅助函数（例如 `setup_logging`），该函数负责从中央配置文件（如 `config/config.yaml`）读取配置，并使用 `logging.config.dictConfig` 应用配置。禁止在各个脚本中单独使用 `logging.basicConfig` 或硬编码日志配置。

## 7.1 依赖管理

*   所有项目依赖的 Python 库及其**固定版本**必须同时在项目根目录的 `requirements.txt` 和 `pyproject.toml` 文件中明确列出。
*   当引入新的第三方依赖包时，必须同步更新 `requirements.txt` 和 `pyproject.toml` 文件。
*   定期更新依赖库，并测试兼容性。
*   使用 `pip freeze > requirements.txt` 生成或更新依赖列表。
*   对于 `pyproject.toml`，确保在 `[project.dependencies]` 或相应部分正确声明依赖。
*   在 `README.md` 中说明如何使用 `requirements.txt` 或 `pyproject.toml` 安装依赖。

## 9. 错误处理规范

*   **异常捕获原则**:
    *   对所有可能失败的操作（如文件 I/O、网络请求、数据格式转换、外部库调用、数值计算中的除零等）使用 `try...except` 块进行健壮的错误处理。
    *   捕获具体的异常类型，避免使用笼统的 `except Exception:` 或 `except:`。例如：
        *   文件操作应捕获 `FileNotFoundError`, `PermissionError`, `IsADirectoryError` 等
        *   网络请求应捕获 `requests.exceptions.RequestException` 或其子类
        *   字典操作应捕获 `KeyError`
        *   数值计算应捕获 `ValueError`, `ZeroDivisionError` 等
        *   第三方库操作应捕获该库定义的特定异常（如 `第三方库名.ErrorType`）
    *   对于无法处理的异常，应记录后重新抛出或转换为自定义异常

*   **自定义异常**:
    *   为项目定义一组有意义的自定义异常类（建议有一个基础异常类），继承自 `Exception`。
    *   自定义异常应包含有意义的错误信息和必要的上下文数据。
    *   示例：
```python
class ProjectBaseError(Exception):
    """本项目的通用基础异常。"""
    pass

class ConfigurationError(ProjectBaseError):
    """配置相关的错误。"""
    def __init__(self, message: str, config_path: str | None = None):
        super().__init__(message)
        self.config_path = config_path

class DataAccessError(ProjectBaseError):
    """数据访问或处理相关的错误。"""
    def __init__(self, message: str, source: str | None = None):
        super().__init__(message)
        self.source = source
```

*   **异常处理最佳实践**:
    *   在 `except` 块中记录详细的错误信息（使用 `logging.error()` 或 `logging.exception()`）
    *   根据情况决定是继续执行、返回错误状态还是重新抛出异常
    *   提供有意义的错误消息给用户或日志系统，包含足够的问题诊断信息
    *   对于可恢复的错误，考虑实现重试机制
    *   避免在 `finally` 块中执行可能抛出异常的操作

*   **异常传播**:
    *   低层函数应抛出具体的异常。
    *   高层函数可以捕获并转换为更抽象的异常，但应保留原始异常信息。
    *   使用 `raise ... from ...` 语法保持异常链。

*   **脚本错误处理与退出策略 (`scripts/` 目录)**:
    *   **目标**: 统一 `scripts/` 目录下脚本的错误处理行为，使其可预测且易于集成。
    *   **核心逻辑**: 脚本中的核心处理函数（执行主要任务的函数）应专注于其业务逻辑。遇到可预期的错误时，应记录日志并**抛出具体的自定义异常**（继承自 `ProjectBaseError` 或其他项目定义的异常）。**禁止在核心逻辑函数中直接调用 `sys.exit()`**。
    *   **`main` 函数**: 每个脚本应有一个 `main` 函数，负责编排任务流程（加载配置、调用核心逻辑函数）。`main` 函数应使用 `try...except` 块捕获其调用的核心逻辑函数可能抛出的**预期**自定义异常。在 `except` 块中，记录详细的错误信息，然后可以通过**返回特定的错误状态**或**重新抛出一个代表脚本执行失败的特定异常**来向上层传递失败信号。**避免在 `main` 函数中直接调用 `sys.exit()`**，除非是处理最终的退出逻辑。
    *   **脚本入口 (`if __name__ == "__main__":`)**:
        *   应包含一个顶层的 `try...except Exception:` 块，用于捕获所有未被 `main` 函数处理的预期或非预期异常。
        *   在此顶层 `except` 块中，**必须**记录详细的错误信息（推荐使用 `logging.exception()` 以包含堆栈跟踪）。
        *   记录错误后，应调用 `sys.exit(1)` 或其他非零状态码退出程序，明确表示脚本执行失败。
        *   示例结构：
```python
import sys
import logging
# 假设已定义以下异常
# from src.exceptions import ConfigurationError, DataAccessError, ProjectBaseError
# from src.core_logic import run_task # 导入核心逻辑
# from src.config import load_config # 导入配置加载

logger = logging.getLogger(__name__)

# 定义一个用于脚本执行失败的标记异常（可选）
class ScriptExecutionError(Exception):
    pass

def main():
    try:
        # config = load_config(...)
        # result = run_task(config)
        logger.info("脚本执行成功。")
        # 如果需要，可以返回 0 或 True 表示成功
        return 0
    except ConfigurationError as e:
        logger.error(f"配置错误: {e}")
        raise ScriptExecutionError("配置加载失败") from e
    except DataAccessError as e:
        logger.error(f"数据处理错误: {e}")
        raise ScriptExecutionError("数据处理失败") from e
    except ProjectBaseError as e: # 捕获其他预期的项目异常
        logger.error(f"发生已知错误: {e}")
        raise ScriptExecutionError("脚本执行中发生已知错误") from e

if __name__ == "__main__":
    exit_code = 1 # 默认为失败
    try:
        # setup_logging() # 配置日志
        main()
        exit_code = 0 # main 成功执行则设为 0
    except ScriptExecutionError as e: # 捕获 main 函数传递的失败信号
        # logger 已在 main 中记录过具体原因
        # 保持 exit_code = 1
        pass
    except Exception:
        logger.exception("发生未预期的严重错误，脚本终止。") # 捕获所有其他异常
        # 保持 exit_code = 1
    finally:
        # 可以在这里添加清理逻辑
        sys.exit(exit_code)
```

## 10. 性能优化指导

### 10.1 基本原则
- **【原则】** 先保证正确性，再考虑性能优化
- **【原则】** 基于实际测量结果进行优化，避免过早优化
- **【原则】** 优先选择算法优化，其次考虑实现优化

### 10.2 常见优化技巧

#### 10.2.1 数据结构选择
```python
# 【推荐】使用适当的数据结构
# 查找操作使用 set 而不是 list
valid_ids = {1, 2, 3, 4, 5}  # O(1) 查找
if user_id in valid_ids:
    pass

# 【推荐】使用 dict 进行键值查找
user_map = {"alice": 1, "bob": 2}  # O(1) 查找
user_id = user_map.get("alice", 0)
```

#### 10.2.2 列表推导式和生成器
```python
# 【推荐】使用列表推导式
squares = [x**2 for x in range(10)]

# 【推荐】对于大数据集使用生成器
squares_gen = (x**2 for x in range(1000000))

# 【推荐】使用内置函数
total = sum(x**2 for x in range(10))
```

#### 10.2.3 字符串操作
```python
# 【不推荐】字符串拼接
result = ""
for item in items:
    result += str(item)

# 【推荐】使用 join
result = "".join(str(item) for item in items)

# 【推荐】使用 f-string
message = f"Hello {name}, you have {count} items"
```

### 10.3 性能测试
```python
import time
import cProfile
from functools import wraps

def timing_decorator(func):
    """性能测试装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} took {end - start:.4f} seconds")
        return result
    return wrapper

# 使用 cProfile 进行详细分析
def profile_function():
    cProfile.run('your_function()', 'profile_stats')
```

## 11. 安全编码规范

### 11.1 输入验证
```python
import re
from typing import Optional

def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def sanitize_filename(filename: str) -> str:
    """清理文件名，防止路径遍历攻击"""
    # 移除危险字符
    safe_filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    # 移除路径遍历字符
    safe_filename = safe_filename.replace('..', '')
    return safe_filename.strip()
```

### 11.2 密码和敏感信息处理
```python
import os
import hashlib
import secrets
from cryptography.fernet import Fernet

# 【强制】从环境变量读取敏感信息
API_KEY = os.getenv('API_KEY')
if not API_KEY:
    raise ValueError("API_KEY environment variable is required")

# 【推荐】密码哈希处理
def hash_password(password: str) -> str:
    """安全的密码哈希"""
    salt = secrets.token_hex(16)
    password_hash = hashlib.pbkdf2_hmac('sha256',
                                       password.encode(),
                                       salt.encode(),
                                       100000)
    return f"{salt}:{password_hash.hex()}"
```

### 11.3 SQL注入防护
```python
import sqlite3

# 【不推荐】字符串拼接
def get_user_bad(user_id: str):
    query = f"SELECT * FROM users WHERE id = {user_id}"
    # 容易受到SQL注入攻击

# 【推荐】参数化查询
def get_user_safe(user_id: int):
    query = "SELECT * FROM users WHERE id = ?"
    cursor.execute(query, (user_id,))
    return cursor.fetchone()
```

### 11.4 文件操作安全
```python
import os
from pathlib import Path

def safe_file_read(filename: str, base_dir: str) -> str:
    """安全的文件读取，防止路径遍历"""
    base_path = Path(base_dir).resolve()
    file_path = (base_path / filename).resolve()

    # 确保文件在指定目录内
    if not str(file_path).startswith(str(base_path)):
        raise ValueError("Invalid file path")

    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {filename}")

    return file_path.read_text()
```

## 12. Git工作流规范

### 12.1 分支命名规范
```
feature/功能描述    # 新功能开发
bugfix/问题描述     # Bug修复
hotfix/紧急修复     # 紧急修复
release/版本号      # 发布准备
```

### 12.2 提交消息规范
遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型 (type):**
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例:**
```
feat(auth): add user authentication system

- Implement JWT token generation
- Add login/logout endpoints
- Add user session management

Closes #123
```

### 12.3 .gitignore 模板
```gitignore
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
*.log
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/
```

## 13. 代码审查规范

### 13.1 审查清单

#### 13.1.1 代码质量
- [ ] 代码符合PEP 8规范
- [ ] 通过所有自动化检查（black, flake8, mypy）
- [ ] 函数和类职责单一
- [ ] 变量和函数命名清晰
- [ ] 无重复代码

#### 13.1.2 功能性
- [ ] 实现满足需求
- [ ] 边界条件处理正确
- [ ] 错误处理完善
- [ ] 性能可接受

#### 13.1.3 可维护性
- [ ] 代码结构清晰
- [ ] 文档字符串完整
- [ ] 注释必要且准确
- [ ] 测试覆盖充分

### 13.2 审查流程
1. **自检**: 提交前自行检查代码
2. **自动检查**: CI/CD自动运行检查工具
3. **同行评审**: 至少一名团队成员评审
4. **修改完善**: 根据反馈修改代码
5. **最终确认**: 审查通过后合并

### 13.3 Pull Request模板
```markdown
## 变更描述
简要描述本次变更的内容和目的。

## 变更类型
- [ ] 新功能 (feature)
- [ ] Bug修复 (bugfix)
- [ ] 代码重构 (refactor)
- [ ] 文档更新 (docs)
- [ ] 其他 (请说明)

## 测试
- [ ] 已添加单元测试
- [ ] 已运行现有测试套件
- [ ] 已进行手动测试

## 检查清单
- [ ] 代码符合编码规范
- [ ] 通过所有自动化检查
- [ ] 更新了相关文档
- [ ] 无重大性能影响

## 相关Issue
Closes #(issue number)
```

## 14. CI/CD集成规范

### 14.1 GitHub Actions配置示例
```yaml
# .github/workflows/python-ci.yml
name: Python CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Lint with flake8
      run: |
        flake8 src/ tests/

    - name: Check code formatting with black
      run: |
        black --check src/ tests/

    - name: Type check with mypy
      run: |
        mypy src/

    - name: Test with pytest
      run: |
        pytest tests/ --cov=src/ --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

### 14.2 质量门禁
- **代码覆盖率**: 最低80%
- **复杂度**: 单个函数不超过10
- **重复度**: 不超过3%
- **安全扫描**: 无高危漏洞

## 15. 一致性原则

*   **一致性是关键**。即使某些风格选择存在争议（例如单引号 vs 双引号），最重要的是在整个项目中保持统一。
*   新代码应遵循现有代码的风格。
*   **API 设计一致性**: 在同一个模块或相关的模块组中，函数和方法的 API 设计（如参数顺序、命名约定、返回值结构、错误处理方式 `get`/`set` 行为等）应保持一致。
*   代码审查是确保一致性的重要环节。

## 16. 单元测试规范

*   **生产代码与测试代码关系原则**: 严禁在生产代码中添加仅为通过测试而存在的逻辑（例如，通过检查 `sys.modules` 来改变特定环境下的行为）。测试代码应负责模拟生产环境的依赖（如配置、外部服务）或使用 Mock/Patch 等技术隔离被测单元，而不是要求生产代码为适应测试而修改。如果生产代码需要特殊处理才能通过测试，这通常表明测试设计或测试环境设置存在问题，应优先审查和改进测试本身。
*   **测试环境设置**:
    *   **【强制】严禁在测试代码 (`tests/` 目录下) 中使用 `sys.path.append()`、`sys.path.insert()` 或类似技巧手动修改 Python 模块搜索路径来导入 `src` 目录下的代码。** 这种做法会破坏测试的可靠性和可移植性。
    *   **【推荐】标准设置方法**:
        **开发模式安装**: 在激活虚拟环境后，于项目根目录下运行 `pip install -e .`。这将以可编辑模式安装你的项目，使 `src` 包对 Python 解释器（包括 `pytest`）可见。这是最推荐的方式。
    *   通过上述标准方法之一，测试代码应该能够直接使用标准的绝对导入语句（如 `from src.utils import geo_utils`）导入被测代码。
*   使用 `pytest` 作为单元测试框架。
*   测试文件应放在 `tests/` 目录下，文件名以 `test_` 开头。
*   每个模块或类都应该有相应的测试模块。

示例：
```python
# tests/test_calculate_area.py
import pytest
from src.your_module import calculate_area

def test_calculate_area_positive():
    assert calculate_area(5, 10) == 50

def test_calculate_area_negative():
    with pytest.raises(ValueError):
        calculate_area(-5, 10)
```

## 17. 检查清单

### 17.1 代码提交前检查清单
- [ ] 代码通过 black 格式化
- [ ] 代码通过 flake8 检查
- [ ] 代码通过 mypy 类型检查
- [ ] 导入语句已排序 (isort)
- [ ] 所有函数/类有文档字符串
- [ ] 添加了必要的类型提示
- [ ] 错误处理完善
- [ ] 日志记录适当
- [ ] 单元测试覆盖新功能
- [ ] 通过所有现有测试
- [ ] 更新了相关文档

### 17.2 代码审查检查清单
- [ ] 代码逻辑正确
- [ ] 性能可接受
- [ ] 安全性考虑充分
- [ ] 代码可读性良好
- [ ] 遵循项目架构设计
- [ ] 无重复代码
- [ ] 异常处理合理
- [ ] 资源管理正确

## 18. 常见问题解答

### 18.1 工具配置问题

**Q: Black 和 Flake8 配置冲突怎么办？**
A: 在 flake8 配置中忽略 E203 和 W503，这些是与 black 兼容性相关的规则。

**Q: MyPy 报告类型错误但代码运行正常？**
A: 可能需要安装类型存根包（如 types-requests），或使用 `# type: ignore` 注释临时忽略。

**Q: Pre-commit 钩子失败怎么办？**
A: 首先运行对应的工具手动检查，修复问题后重新提交。可以使用 `--no-verify` 跳过钩子，但不推荐。

### 18.2 编码规范问题

**Q: 什么时候可以违反 88 字符行长限制？**
A: 在以下情况下可以适当放宽：
- 长的 URL 或路径
- 正则表达式
- 长的字符串字面量

**Q: 如何处理遗留代码不符合规范的情况？**
A: 新代码必须符合规范，遗留代码在修改时逐步改进，避免大规模重构。

## 19. 违规处理流程

### 19.1 自动检查违规
- **CI/CD 失败**: 代码不能合并，必须修复后重新提交
- **Pre-commit 失败**: 提交被阻止，修复后重新提交

### 19.2 人工审查违规
1. **首次违规**: 审查者指出问题，开发者修改
2. **重复违规**: 团队负责人介入，进行规范培训
3. **严重违规**: 可能影响项目质量和安全的违规，需要立即修复

### 19.3 规范更新流程
1. **提议**: 任何团队成员可以提议规范更新
2. **讨论**: 团队讨论更新的必要性和影响
3. **试行**: 在小范围内试行新规范
4. **正式发布**: 更新文档并通知全团队
5. **培训**: 组织培训确保团队理解新规范

## 20. 附录：配置文件示例

### 20.1 pyproject.toml 完整示例
```toml
[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "your-project"
description = "项目描述"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "requests>=2.28.0",
    "pydantic>=1.10.0",
    "click>=8.0.0",
]
dynamic = ["version"]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "isort>=5.12.0",
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pre-commit>=3.0.0",
]

[project.urls]
Homepage = "https://github.com/yourorg/yourproject"
Repository = "https://github.com/yourorg/yourproject"
Documentation = "https://yourproject.readthedocs.io"
"Bug Tracker" = "https://github.com/yourorg/yourproject/issues"

[tool.setuptools_scm]

[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  \.eggs
  | \.git
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = ["pytest", "requests", "pydantic", "click"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

[tool.coverage.run]
source = ["src"]
omit = ["*/tests/*", "*/test_*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
```

### 20.2 requirements.txt 示例
```txt
# 生产依赖
requests==2.31.0
pydantic==2.4.2
click==8.1.7
python-dotenv==1.0.0
loguru==0.7.2

# 数据处理
pandas==2.1.1
numpy==1.24.3

# Web框架 (如果需要)
fastapi==0.103.1
uvicorn==0.23.2
```

### 20.3 requirements-dev.txt 示例
```txt
# 开发工具
black==23.9.1
flake8==6.1.0
mypy==1.6.1
isort==5.12.0
pre-commit==3.5.0

# 测试工具
pytest==7.4.2
pytest-cov==4.1.0
pytest-mock==3.11.1
pytest-asyncio==0.21.1

# 安全检查
bandit==1.7.5
safety==2.3.5

# 文档工具
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# 调试工具
ipdb==0.13.13
rich==13.6.0
```

---

## 文档变更历史

| 版本 | 日期 | 变更内容 | 修改人 |
|------|------|----------|--------|
| v2.0.0 | 2025-06-24 | 全面重构，添加工具配置、安全规范、Git工作流等 | 研发团队 |
| v1.0.0 | 2025-04-01 | 初始版本，基础编码规范 | 研发团队 |

---

**本规范是团队开发的重要指导文件，请所有团队成员认真遵循。如有疑问或建议，请及时与技术负责人沟通。**