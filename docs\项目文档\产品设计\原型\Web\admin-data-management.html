<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地质灾害点管理 - 茂名市地质灾害预警平台</title>
    <link rel="stylesheet" href="../common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 数据管理页面样式 */
        .data-layout {
            display: flex;
            min-height: 100vh;
        }

        .data-content {
            flex: 1;
            margin-left: 240px;
            background-color: var(--admin-bg-primary);
        }

        .data-header {
            background-color: var(--admin-bg-secondary);
            border-bottom: 1px solid var(--admin-border);
            padding: var(--spacing-lg);
        }

        .data-main {
            padding: var(--spacing-lg);
        }

        .header-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .search-filters {
            background-color: var(--admin-bg-secondary);
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .filter-label {
            color: var(--admin-text-primary);
            font-size: 14px;
            font-weight: 500;
        }

        .data-table-container {
            background-color: var(--admin-bg-secondary);
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .table-header {
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--admin-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            color: var(--admin-text-primary);
            font-size: 18px;
            font-weight: 600;
        }

        .table-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background-color: var(--admin-bg-primary);
            color: var(--admin-text-primary);
            padding: var(--spacing-md);
            text-align: left;
            font-weight: 600;
            border-bottom: 1px solid var(--admin-border);
        }

        .data-table td {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--admin-border);
            color: var(--admin-text-secondary);
        }

        .data-table tbody tr:hover {
            background-color: var(--admin-bg-primary);
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: white;
        }

        .status-active { background-color: var(--admin-success); }
        .status-inactive { background-color: var(--admin-text-tertiary); }
        .status-high { background-color: var(--admin-danger); }
        .status-medium { background-color: var(--admin-warning); }
        .status-low { background-color: var(--admin-success); }

        .action-buttons {
            display: flex;
            gap: var(--spacing-xs);
        }

        .action-btn {
            width: 28px;
            height: 28px;
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-md);
            background-color: var(--admin-bg-tertiary);
            color: var(--admin-text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 12px;
        }

        .action-btn:hover {
            border-color: var(--admin-primary);
            color: var(--admin-primary);
        }

        .action-btn.edit:hover {
            border-color: var(--admin-info);
            color: var(--admin-info);
        }

        .action-btn.delete:hover {
            border-color: var(--admin-danger);
            color: var(--admin-danger);
        }

        .pagination-container {
            padding: var(--spacing-lg);
            border-top: 1px solid var(--admin-border);
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .pagination-info {
            color: var(--admin-text-secondary);
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: var(--spacing-sm);
        }

        .page-btn {
            width: 32px;
            height: 32px;
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-md);
            background-color: var(--admin-bg-tertiary);
            color: var(--admin-text-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .page-btn:hover {
            border-color: var(--admin-primary);
            color: var(--admin-primary);
        }

        .page-btn.active {
            background-color: var(--admin-primary);
            border-color: var(--admin-primary);
            color: white;
        }

        .bulk-actions {
            background-color: var(--admin-bg-tertiary);
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            display: none;
        }

        .bulk-actions.show {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .bulk-text {
            color: var(--admin-text-primary);
            font-size: 14px;
        }

        .bulk-buttons {
            display: flex;
            gap: var(--spacing-sm);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .data-content {
                margin-left: 64px;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .data-table {
                font-size: 12px;
            }

            .data-table th,
            .data-table td {
                padding: var(--spacing-sm);
            }
        }
    </style>
</head>
<body class="admin-theme">
    <div class="data-layout">
        <!-- 侧边栏（复用仪表板的侧边栏） -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="sidebar-title">管理后台</div>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-group">
                    <div class="nav-group-title">主要功能</div>
                    <a href="admin-dashboard.html" class="nav-item">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span>仪表板</span>
                    </a>
                    <a href="#" class="nav-item active">
                        <i class="fas fa-map-marked-alt nav-icon"></i>
                        <span>地质灾害点</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-shield-alt nav-icon"></i>
                        <span>风险防范区</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-exclamation-triangle nav-icon"></i>
                        <span>预警发布</span>
                    </a>
                </div>

                <div class="nav-group">
                    <div class="nav-group-title">系统管理</div>
                    <a href="#" class="nav-item">
                        <i class="fas fa-users nav-icon"></i>
                        <span>用户管理</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-key nav-icon"></i>
                        <span>权限管理</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-history nav-icon"></i>
                        <span>操作日志</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-cog nav-icon"></i>
                        <span>系统配置</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- 主要内容区域 -->
        <div class="data-content">
            <!-- 顶部工具栏 -->
            <div class="top-bar">
                <div class="breadcrumb">
                    <i class="fas fa-home"></i>
                    <span class="breadcrumb-separator">/</span>
                    <span>数据管理</span>
                    <span class="breadcrumb-separator">/</span>
                    <span>地质灾害点</span>
                </div>

                <div class="top-bar-actions">
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>

                    <div class="user-menu">
                        <div class="user-avatar">管</div>
                        <div>
                            <div style="color: var(--admin-text-primary); font-size: 14px; font-weight: 500;">管理员</div>
                            <div style="color: var(--admin-text-tertiary); font-size: 12px;">在线</div>
                        </div>
                        <i class="fas fa-chevron-down" style="color: var(--admin-text-tertiary);"></i>
                    </div>
                </div>
            </div>

            <!-- 页面头部 -->
            <div class="data-header">
                <h1 class="text-h2" style="color: var(--admin-text-primary);">地质灾害点管理</h1>
                <p style="color: var(--admin-text-secondary); margin-top: var(--spacing-sm);">
                    管理全市地质灾害点信息，包括新增、编辑、删除和状态更新
                </p>

                <div class="header-actions">
                    <button class="btn btn-primary-admin">
                        <i class="fas fa-plus mr-2"></i>新增灾害点
                    </button>
                    <button class="btn btn-secondary-admin">
                        <i class="fas fa-upload mr-2"></i>批量导入
                    </button>
                    <button class="btn btn-secondary-admin">
                        <i class="fas fa-download mr-2"></i>导出数据
                    </button>
                </div>
            </div>

            <!-- 主要内容 -->
            <div class="data-main">
                <!-- 搜索和筛选 -->
                <div class="search-filters">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label class="filter-label">搜索关键词</label>
                            <input type="text" class="input input-admin" placeholder="输入灾害点名称或位置">
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">所属区县</label>
                            <select class="input input-admin">
                                <option value="">全部区县</option>
                                <option value="maonan">茂南区</option>
                                <option value="dianbai">电白区</option>
                                <option value="gaozhou">高州市</option>
                                <option value="huazhou">化州市</option>
                                <option value="xinyi">信宜市</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">灾害类型</label>
                            <select class="input input-admin">
                                <option value="">全部类型</option>
                                <option value="landslide">滑坡</option>
                                <option value="collapse">崩塌</option>
                                <option value="debris-flow">泥石流</option>
                                <option value="ground-collapse">地面塌陷</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">风险等级</label>
                            <select class="input input-admin">
                                <option value="">全部等级</option>
                                <option value="high">高风险</option>
                                <option value="medium">中风险</option>
                                <option value="low">低风险</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex gap-md">
                        <button class="btn btn-primary-admin">
                            <i class="fas fa-search mr-2"></i>搜索
                        </button>
                        <button class="btn btn-secondary-admin">
                            <i class="fas fa-redo mr-2"></i>重置
                        </button>
                    </div>
                </div>

                <!-- 批量操作栏 -->
                <div class="bulk-actions" id="bulkActions">
                    <div class="bulk-text">已选择 <span id="selectedCount">0</span> 项</div>
                    <div class="bulk-buttons">
                        <button class="btn btn-secondary-admin">
                            <i class="fas fa-edit mr-2"></i>批量编辑
                        </button>
                        <button class="btn btn-secondary-admin">
                            <i class="fas fa-download mr-2"></i>导出选中
                        </button>
                        <button class="btn btn-secondary-admin" style="color: var(--admin-danger); border-color: var(--admin-danger);">
                            <i class="fas fa-trash mr-2"></i>批量删除
                        </button>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="data-table-container">
                    <div class="table-header">
                        <h3 class="table-title">地质灾害点列表</h3>
                        <div class="table-actions">
                            <button class="btn btn-secondary-admin" style="padding: 6px 12px; font-size: 12px;">
                                <i class="fas fa-sync-alt mr-1"></i>刷新
                            </button>
                        </div>
                    </div>

                    <table class="data-table">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" style="accent-color: var(--admin-primary);">
                                </th>
                                <th>编号</th>
                                <th>灾害点名称</th>
                                <th>所属区县</th>
                                <th>灾害类型</th>
                                <th>风险等级</th>
                                <th>威胁人口</th>
                                <th>状态</th>
                                <th>更新时间</th>
                                <th width="120">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox" class="row-select" style="accent-color: var(--admin-primary);"></td>
                                <td>GD001</td>
                                <td>茂南区站前路滑坡点</td>
                                <td>茂南区</td>
                                <td>滑坡</td>
                                <td><span class="status-badge status-high">高风险</span></td>
                                <td>120人</td>
                                <td><span class="status-badge status-active">正常</span></td>
                                <td>2025-07-10</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="row-select" style="accent-color: var(--admin-primary);"></td>
                                <td>GD002</td>
                                <td>电白区海滨大道防范区</td>
                                <td>电白区</td>
                                <td>地面塌陷</td>
                                <td><span class="status-badge status-medium">中风险</span></td>
                                <td>80人</td>
                                <td><span class="status-badge status-active">正常</span></td>
                                <td>2025-07-09</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="row-select" style="accent-color: var(--admin-primary);"></td>
                                <td>GD003</td>
                                <td>高州市文明路崩塌点</td>
                                <td>高州市</td>
                                <td>崩塌</td>
                                <td><span class="status-badge status-low">低风险</span></td>
                                <td>45人</td>
                                <td><span class="status-badge status-active">正常</span></td>
                                <td>2025-07-08</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="row-select" style="accent-color: var(--admin-primary);"></td>
                                <td>GD004</td>
                                <td>化州市工业大道泥石流点</td>
                                <td>化州市</td>
                                <td>泥石流</td>
                                <td><span class="status-badge status-high">高风险</span></td>
                                <td>200人</td>
                                <td><span class="status-badge status-inactive">维护中</span></td>
                                <td>2025-07-07</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox" class="row-select" style="accent-color: var(--admin-primary);"></td>
                                <td>GD005</td>
                                <td>信宜市北界镇滑坡群</td>
                                <td>信宜市</td>
                                <td>滑坡</td>
                                <td><span class="status-badge status-medium">中风险</span></td>
                                <td>150人</td>
                                <td><span class="status-badge status-active">正常</span></td>
                                <td>2025-07-06</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="action-btn edit" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn delete" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- 分页 -->
                    <div class="pagination-container">
                        <div class="pagination-info">
                            显示第 1-5 条，共 74,215 条记录
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">...</button>
                            <button class="page-btn">14844</button>
                            <button class="page-btn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全选功能
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.row-select');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');

            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });

            if (this.checked) {
                bulkActions.classList.add('show');
                selectedCount.textContent = checkboxes.length;
            } else {
                bulkActions.classList.remove('show');
                selectedCount.textContent = '0';
            }
        });

        // 单行选择
        document.querySelectorAll('.row-select').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const allCheckboxes = document.querySelectorAll('.row-select');
                const checkedBoxes = document.querySelectorAll('.row-select:checked');
                const selectAll = document.getElementById('selectAll');
                const bulkActions = document.getElementById('bulkActions');
                const selectedCount = document.getElementById('selectedCount');

                // 更新全选状态
                selectAll.checked = checkedBoxes.length === allCheckboxes.length;
                selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < allCheckboxes.length;

                // 显示/隐藏批量操作
                if (checkedBoxes.length > 0) {
                    bulkActions.classList.add('show');
                    selectedCount.textContent = checkedBoxes.length;
                } else {
                    bulkActions.classList.remove('show');
                    selectedCount.textContent = '0';
                }
            });
        });

        // 操作按钮点击事件
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const row = this.closest('tr');
                const name = row.cells[2].textContent;
                const action = this.title;

                if (action === '删除') {
                    if (confirm(`确定要删除"${name}"吗？`)) {
                        alert(`已删除：${name}`);
                    }
                } else {
                    alert(`${action}：${name}`);
                }
            });
        });

        // 导航菜单交互
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                if (this.href === '#') {
                    e.preventDefault();
                    alert('功能开发中...');
                }
            });
        });
    </script>
</body>
</html>
