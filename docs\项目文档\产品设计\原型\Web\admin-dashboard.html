<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统仪表板 - 茂名市地质灾害预警平台</title>
    <link rel="stylesheet" href="../common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 基于V3简约实用风的优化重构 - 解决原有样式冲突 */

        /* 重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #0f172a;
            color: #e2e8f0;
            min-height: 100vh;
            overflow-x: hidden;
            line-height: 1.6;
        }

        .dashboard-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧导航栏 - 增加展开/收起功能 */
        .sidebar {
            width: 240px;
            background: #1e293b;
            border-right: 1px solid #475569;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
            z-index: 100;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
            transition: width 0.3s ease;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        /* 确保sidebar-header和top-bar高度完全一致 - 关键修复 */
        .sidebar-header {
            height: 64px;
            padding: 0 20px;
            border-bottom: 1px solid #475569;
            display: flex;
            align-items: center;
            gap: 12px;
            background: #1e293b;
            position: relative;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 0 14px;
            justify-content: center;
        }

        .sidebar-logo {
            width: 32px;
            height: 32px;
            background: #3b82f6;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .sidebar-title {
            color: #f1f5f9;
            font-size: 16px;
            font-weight: 600;
            white-space: nowrap;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .sidebar-title {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }



        .sidebar-nav {
            padding: 16px 0;
        }

        .nav-group {
            margin-bottom: 24px;
        }

        .nav-group-title {
            color: #94a3b8;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0 20px;
            margin-bottom: 8px;
            white-space: nowrap;
            opacity: 1;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .nav-group-title {
            opacity: 0;
            height: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.15s ease;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            border-radius: 0;
            position: relative;
        }

        .sidebar.collapsed .nav-item {
            padding: 10px 14px;
            justify-content: center;
        }

        .nav-item:hover {
            background: #334155;
            color: #f1f5f9;
        }

        .nav-item:active {
            background: #475569;
            transform: scale(0.98);
        }

        .nav-item.active {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            border-right: 3px solid #3b82f6;
            font-weight: 600;
        }

        .nav-icon {
            width: 16px;
            margin-right: 12px;
            text-align: center;
            font-size: 14px;
            flex-shrink: 0;
        }

        .sidebar.collapsed .nav-icon {
            margin-right: 0;
        }

        .nav-text {
            white-space: nowrap;
            opacity: 1;
            transition: opacity 0.3s ease;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar.collapsed .nav-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        /* 收起状态下的提示 */
        .sidebar.collapsed .nav-item:hover::after {
            content: attr(data-title);
            position: absolute;
            left: 60px;
            top: 50%;
            transform: translateY(-50%);
            background: #1e293b;
            color: #f1f5f9;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            white-space: nowrap;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border: 1px solid #475569;
            z-index: 1000;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            background: #0f172a;
            transition: margin-left 0.3s ease;
        }

        .sidebar.collapsed + .main-content {
            margin-left: 60px;
        }

        /* 汉堡菜单按钮 - 桌面端和移动端通用 */
        .menu-toggle-btn {
            display: flex;
            width: 36px;
            height: 36px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            align-items: center;
            justify-content: center;
            color: #cbd5e1;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .menu-toggle-btn:hover {
            background: #475569;
            color: #f1f5f9;
        }

        .menu-toggle-btn:active {
            background: #64748b;
            transform: scale(0.95);
        }

        /* 移动端遮罩层 */
        .mobile-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 99;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .mobile-overlay.show {
            display: block;
            opacity: 1;
        }

        /* 顶部工具栏 - 与sidebar-header高度完全一致 */
        .top-bar {
            height: 64px;
            background: #1e293b;
            border-bottom: 1px solid #475569;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            position: sticky;
            top: 0;
            z-index: 50;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #94a3b8;
            font-size: 14px;
            font-weight: 500;
        }

        .breadcrumb-current {
            color: #f1f5f9;
            font-weight: 600;
        }

        .breadcrumb-separator {
            color: #64748b;
        }

        .top-bar-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .notification-btn {
            position: relative;
            width: 36px;
            height: 36px;
            border-radius: 6px;
            background: #334155;
            border: 1px solid #475569;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #cbd5e1;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .notification-btn:hover {
            background: #475569;
            border-color: #64748b;
            color: #f1f5f9;
        }

        .notification-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            background: #ef4444;
            border: 2px solid #1e293b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            font-weight: 600;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.15s ease;
            background: #334155;
            border: 1px solid #475569;
        }

        .user-menu:hover {
            background: #475569;
            border-color: #64748b;
        }

        .user-avatar {
            width: 24px;
            height: 24px;
            background: #3b82f6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 11px;
            font-weight: 600;
        }

        .user-name {
            color: #f1f5f9;
            font-size: 14px;
            font-weight: 500;
        }

        /* 仪表板内容 */
        .dashboard-content {
            padding: 24px;
        }

        /* 数据统计卡片组 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #1e293b;
            border: 1px solid #475569;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.15s ease;
        }

        .stat-card:hover {
            border-color: #64748b;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .stat-title {
            font-size: 14px;
            font-weight: 500;
            color: #94a3b8;
        }

        .stat-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            font-weight: 600;
        }

        .icon-blue { background: #3b82f6; }
        .icon-green { background: #10b981; }
        .icon-orange { background: #f59e0b; }
        .icon-red { background: #ef4444; }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #f1f5f9;
            margin-bottom: 4px;
            line-height: 1;
        }

        .stat-change {
            font-size: 13px;
            color: #94a3b8;
            font-weight: 500;
        }

        /* 内容区域网格 */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .content-panel {
            background: #1e293b;
            border: 1px solid #475569;
            border-radius: 8px;
            padding: 20px;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #475569;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: #f1f5f9;
        }

        .panel-action {
            color: #3b82f6;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
        }

        .panel-action:hover {
            color: #2563eb;
        }

        /* 快捷操作区域 */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .action-card {
            background: #1e293b;
            border: 1px solid #475569;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .action-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
        }

        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            background: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 16px;
            color: white;
            font-weight: 600;
        }

        .action-title {
            font-size: 14px;
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 4px;
        }

        .action-desc {
            font-size: 12px;
            color: #94a3b8;
            line-height: 1.4;
        }

        /* 系统状态监控面板 */

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #334155;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-name {
            font-weight: 500;
            color: #f1f5f9;
            font-size: 14px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .badge-success {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .badge-warning {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .badge-error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        /* 最近操作日志列表 */
        .log-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .log-item {
            display: flex;
            gap: 12px;
            padding: 8px 0;
            border-bottom: 1px solid #334155;
            font-size: 13px;
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .log-time {
            color: #94a3b8;
            min-width: 60px;
            font-weight: 500;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        }

        .log-content {
            flex: 1;
            color: #cbd5e1;
            line-height: 1.5;
        }

        .log-level {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            margin-right: 6px;
        }

        .level-info {
            background: rgba(59, 130, 246, 0.2);
            color: #3b82f6;
        }

        .level-warn {
            background: rgba(245, 158, 11, 0.2);
            color: #f59e0b;
        }

        .level-error {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .content-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 60px;
            }

            .sidebar-title,
            .nav-group-title,
            .nav-text {
                opacity: 0;
                width: 0;
                overflow: hidden;
            }

            .sidebar-header {
                padding: 0 14px;
                justify-content: center;
            }

            .nav-item {
                padding: 10px 14px;
                justify-content: center;
            }

            .nav-icon {
                margin-right: 0;
            }

            .main-content {
                margin-left: 60px;
            }
        }

        @media (max-width: 768px) {
            /* 移动端样式保持不变 */

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 240px;
                z-index: 100;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .sidebar.collapsed {
                width: 240px;
            }

            .sidebar.open .sidebar-title,
            .sidebar.open .nav-group-title,
            .sidebar.open .nav-text {
                opacity: 1;
                width: auto;
            }

            .sidebar.open .sidebar-header {
                padding: 0 20px;
                justify-content: flex-start;
            }

            .sidebar.open .nav-item {
                padding: 10px 20px;
                justify-content: flex-start;
            }

            .sidebar.open .nav-icon {
                margin-right: 12px;
            }

            .main-content {
                margin-left: 0;
                width: 100%;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .dashboard-content {
                padding: 16px;
            }

            .breadcrumb {
                display: none;
            }

            /* 确保移动端没有空白 */
            body {
                overflow-x: hidden;
            }

            .dashboard-layout {
                width: 100%;
                overflow-x: hidden;
            }
        }

        @media (max-width: 480px) {
            .page-content {
                padding: 16px;
            }

            .stats-grid {
                gap: 12px;
            }

            .stat-card {
                padding: 16px;
            }
        }

        /* 无障碍访问优化 */
        .action-card:focus,
        .notification-btn:focus,
        .user-menu:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* 移除导航项的focus轮廓线 */
        .nav-item:focus {
            outline: none;
        }
    </style>
</head>
<body>
    <!-- 移动端遮罩层 -->
    <div class="mobile-overlay" onclick="closeMobileSidebar()"></div>

    <div class="dashboard-layout">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-mountain"></i>
                </div>
                <div class="sidebar-title">地质灾害预警</div>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-group">
                    <div class="nav-group-title">主要功能</div>
                    <a href="#" class="nav-item active" data-title="系统仪表板">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">系统仪表板</span>
                    </a>
                    <a href="#" class="nav-item" data-title="监测管理">
                        <i class="fas fa-map-marked-alt nav-icon"></i>
                        <span class="nav-text">监测管理</span>
                    </a>
                    <a href="#" class="nav-item" data-title="预警管理">
                        <i class="fas fa-exclamation-triangle nav-icon"></i>
                        <span class="nav-text">预警管理</span>
                    </a>
                    <a href="#" class="nav-item" data-title="数据管理">
                        <i class="fas fa-database nav-icon"></i>
                        <span class="nav-text">数据管理</span>
                    </a>
                </div>

                <div class="nav-group">
                    <div class="nav-group-title">系统管理</div>
                    <a href="admin-user-management.html" class="nav-item" data-title="用户管理">
                        <i class="fas fa-users nav-icon"></i>
                        <span class="nav-text">用户管理</span>
                    </a>
                    <a href="#" class="nav-item" data-title="系统设置">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">系统设置</span>
                    </a>
                    <a href="#" class="nav-item" data-title="统计报告">
                        <i class="fas fa-chart-bar nav-icon"></i>
                        <span class="nav-text">统计报告</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 顶部工具栏 -->
            <div class="top-bar">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <button class="menu-toggle-btn" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="breadcrumb">
                        <span>首页</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-current">系统仪表板</span>
                    </div>
                </div>
                <div class="top-bar-actions">
                    <div class="notification-btn" title="通知">
                        <i class="fas fa-bell"></i>
                        <div class="notification-badge">3</div>
                    </div>
                    <div class="user-menu">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="user-name">管理员</span>
                        <i class="fas fa-chevron-down" style="font-size: 10px; color: #64748b;"></i>
                    </div>
                </div>
            </div>

            <!-- 仪表板内容 -->
            <div class="dashboard-content">

                <!-- 数据统计卡片组（4个关键指标） -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">监测点位总数</div>
                            <div class="stat-icon icon-blue">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                        </div>
                        <div class="stat-value">74,215</div>
                        <div class="stat-change">覆盖全市90个镇街</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">在线监测点</div>
                            <div class="stat-icon icon-green">
                                <i class="fas fa-wifi"></i>
                            </div>
                        </div>
                        <div class="stat-value">74,180</div>
                        <div class="stat-change">在线率 99.95%</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">今日预警</div>
                            <div class="stat-icon icon-orange">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                        </div>
                        <div class="stat-value">3</div>
                        <div class="stat-change">最近预警 2小时前</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-title">活跃用户</div>
                            <div class="stat-icon icon-red">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-value">1,247</div>
                        <div class="stat-change">当前在线 8人</div>
                    </div>
                </div>

                <!-- 快捷操作区域 -->
                <div class="content-panel" style="margin-bottom: 16px;">
                    <div class="panel-header">
                        <h2 class="panel-title">快捷操作</h2>
                        <a href="#" class="panel-action">查看全部</a>
                    </div>
                    <div class="quick-actions">
                        <div class="action-card" tabindex="0">
                            <div class="action-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="action-title">新增监测点</div>
                            <div class="action-desc">添加新的地质灾害监测点</div>
                        </div>
                        <div class="action-card" tabindex="0">
                            <div class="action-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div class="action-title">发布预警</div>
                            <div class="action-desc">发布地质灾害预警信息</div>
                        </div>
                        <div class="action-card" tabindex="0">
                            <div class="action-icon">
                                <i class="fas fa-upload"></i>
                            </div>
                            <div class="action-title">数据导入</div>
                            <div class="action-desc">批量导入监测数据</div>
                        </div>
                        <div class="action-card" tabindex="0">
                            <div class="action-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="action-title">统计报告</div>
                            <div class="action-desc">生成数据统计报告</div>
                        </div>
                    </div>
                </div>

                <!-- 内容网格 -->
                <div class="content-grid">
                    <!-- 系统状态监控面板 -->
                    <div class="content-panel">
                        <div class="panel-header">
                            <h2 class="panel-title">系统状态监控</h2>
                            <a href="#" class="panel-action">详细信息</a>
                        </div>
                        <div class="status-item">
                            <span class="status-name">Web服务器</span>
                            <span class="status-badge badge-success">运行正常</span>
                        </div>
                        <div class="status-item">
                            <span class="status-name">数据库服务</span>
                            <span class="status-badge badge-success">运行正常</span>
                        </div>
                        <div class="status-item">
                            <span class="status-name">API接口服务</span>
                            <span class="status-badge badge-success">运行正常</span>
                        </div>
                        <div class="status-item">
                            <span class="status-name">预警推送服务</span>
                            <span class="status-badge badge-warning">运行异常</span>
                        </div>
                        <div class="status-item">
                            <span class="status-name">数据同步服务</span>
                            <span class="status-badge badge-success">运行正常</span>
                        </div>
                    </div>

                    <!-- 最近操作日志列表 -->
                    <div class="content-panel">
                        <div class="panel-header">
                            <h2 class="panel-title">最近操作日志</h2>
                            <a href="#" class="panel-action">查看更多</a>
                        </div>
                        <div class="log-list">
                            <div class="log-item">
                                <div class="log-time">23:28</div>
                                <div class="log-content">
                                    <span class="log-level level-info">INFO</span>
                                    管理员 admin 登录系统
                                </div>
                            </div>
                            <div class="log-item">
                                <div class="log-time">23:27</div>
                                <div class="log-content">
                                    <span class="log-level level-warn">WARN</span>
                                    茂南区新坡镇发布黄色预警
                                </div>
                            </div>
                            <div class="log-item">
                                <div class="log-time">23:26</div>
                                <div class="log-content">
                                    <span class="log-level level-info">INFO</span>
                                    数据库备份完成
                                </div>
                            </div>
                            <div class="log-item">
                                <div class="log-time">23:25</div>
                                <div class="log-content">
                                    <span class="log-level level-info">INFO</span>
                                    向1,247名用户发送预警通知
                                </div>
                            </div>
                            <div class="log-item">
                                <div class="log-time">23:24</div>
                                <div class="log-content">
                                    <span class="log-level level-error">ERROR</span>
                                    监测点MM001连接超时
                                </div>
                            </div>
                            <div class="log-item">
                                <div class="log-time">23:23</div>
                                <div class="log-content">
                                    <span class="log-level level-info">INFO</span>
                                    系统自动备份启动
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 统一的侧边栏切换功能 - 桌面端和移动端通用
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuIcon = document.querySelector('.menu-toggle-btn i');

            // 移动端：显示/隐藏侧边栏
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('open');

                if (sidebar.classList.contains('open')) {
                    overlay.classList.add('show');
                    document.body.style.overflow = 'hidden';
                    menuIcon.className = 'fas fa-times'; // 显示关闭图标
                } else {
                    overlay.classList.remove('show');
                    document.body.style.overflow = '';
                    menuIcon.className = 'fas fa-bars'; // 显示汉堡图标
                }
            }
            // 桌面端：展开/收起侧边栏
            else {
                sidebar.classList.toggle('collapsed');

                if (sidebar.classList.contains('collapsed')) {
                    menuIcon.className = 'fas fa-indent'; // 收起状态：显示展开图标
                } else {
                    menuIcon.className = 'fas fa-outdent'; // 展开状态：显示收起图标
                }
            }
        }

        // 关闭移动端侧边栏
        function closeMobileSidebar() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuIcon = document.querySelector('.menu-toggle-btn i');

            sidebar.classList.remove('open');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
            menuIcon.className = 'fas fa-bars'; // 恢复汉堡图标
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            const menuIcon = document.querySelector('.menu-toggle-btn i');

            if (window.innerWidth > 768) {
                sidebar.classList.remove('open');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }

            // 在平板尺寸自动收起侧边栏
            if (window.innerWidth <= 1024 && window.innerWidth > 768) {
                sidebar.classList.add('collapsed');
                menuIcon.className = 'fas fa-indent'; // 收起状态图标
            } else if (window.innerWidth > 1024) {
                sidebar.classList.remove('collapsed');
                menuIcon.className = 'fas fa-outdent'; // 展开状态图标
            }

            // 移动端恢复汉堡图标
            if (window.innerWidth <= 768) {
                menuIcon.className = 'fas fa-bars';
            }
        });

        // 基于V3简约实用风的交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            console.log('茂名市地质灾害预警平台 - 简约实用版');

            // 初始化响应式状态
            const sidebar = document.querySelector('.sidebar');
            const menuIcon = document.querySelector('.menu-toggle-btn i');

            if (window.innerWidth <= 1024 && window.innerWidth > 768) {
                sidebar.classList.add('collapsed');
                menuIcon.className = 'fas fa-indent'; // 收起状态图标
            } else if (window.innerWidth > 1024) {
                menuIcon.className = 'fas fa-outdent'; // 展开状态图标
            } else {
                menuIcon.className = 'fas fa-bars'; // 移动端汉堡图标
            }

            // 快捷操作点击事件
            document.querySelectorAll('.action-card').forEach(card => {
                card.addEventListener('click', function() {
                    const title = this.querySelector('.action-title').textContent;
                    console.log('执行操作：' + title);

                    // 简单的确认提示
                    if (confirm(`确认要执行"${title}"操作吗？`)) {
                        console.log(`执行${title}操作`);
                    }
                });

                // 键盘访问支持
                card.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }
                });
            });

            // 导航项点击事件
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    // 如果有实际链接且不是#，则允许跳转
                    const href = this.getAttribute('href');
                    if (href && href !== '#' && href !== 'javascript:void(0)') {
                        // 允许正常跳转，不阻止默认行为
                        return;
                    }

                    // 只有空链接或#链接才阻止默认行为
                    e.preventDefault();

                    // 移除所有active状态
                    document.querySelectorAll('.nav-item').forEach(nav => {
                        nav.classList.remove('active');
                    });

                    // 添加active状态到当前项
                    this.classList.add('active');

                    // 更新面包屑
                    const itemText = this.textContent.trim();
                    const breadcrumb = document.querySelector('.breadcrumb');
                    breadcrumb.innerHTML = `
                        <span>首页</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-current">${itemText}</span>
                    `;
                });
            });

            // 通知按钮点击
            document.querySelector('.notification-btn').addEventListener('click', function() {
                alert('您有3条新通知：\n1. 茂南区新坡镇发布黄色预警\n2. 监测点MM001连接超时\n3. 系统自动备份完成');
            });

            // 用户菜单点击
            document.querySelector('.user-menu').addEventListener('click', function() {
                const menu = confirm('用户菜单\n\n1. 个人设置\n2. 修改密码\n3. 退出登录\n\n点击确定退出登录');
                if (menu) {
                    window.location.href = 'admin-login.html';
                }
            });

            // 简约实用风特色功能
            console.log('简约实用风设计已加载');
            console.log('支持键盘导航和无障碍访问');
        });
    </script>
</body>
</html>
