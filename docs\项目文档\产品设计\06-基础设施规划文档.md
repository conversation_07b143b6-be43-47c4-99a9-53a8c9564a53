# 基础设施规划文档

---

## 1. 文档信息

| 属性 | 值 |
| :--- | :--- |
| **项目名称** | 茂名市地质灾害预警平台 |
| **文档版本** | V1.1 |
| **文档状态** | 已完成 |
| **创建日期** | 2025-07-06 |
| **最后更新日期** | 2025-07-09 |
| **作者** | 梁铭显 |
| **审核者** | 待定 |

---

## 2. 修订历史

| 版本号 | 修订日期 | 修订内容摘要 | 修订人 |
| :--- | :--- | :--- | :--- |
| V1.0 | 2025-07-06 | 创建初始版本 | 梁铭显 |
| V1.1 | 2025-07-09 | 更新云服务器及短信价格 | 梁铭显 |

---

## 3. 执行摘要

本文档详细规划了茂名市地质灾害预警平台的基础设施需求，包括服务器配置、网络架构、存储方案和第三方服务集成等内容。基础设施规划以支持约472万用户查询、99.5%系统可用性和<3秒查询响应时间为目标，为项目实施提供技术基础。

---

## 4. 服务器配置方案

### 4.1. 服务器硬件配置

| 配置项 | 规格 | 说明 |
| :--- | :--- | :--- |
| **实例规格** | 16核64G | 适合中小规模Web应用和数据处理 |
| **操作系统** | Ubuntu 24.04 64位 | 最新LTS版本，适合Python开发环境 |
| **系统盘** | ESSD云盘，100GiB，PL1性能级别 | 提供高I/O性能，适合系统和应用运行 |
| **数据盘** | 增强型SSD云盘，500GiB，性能级别2 | 提供高IOPS和低延迟，适合数据库操作 |
| **带宽** | 固定带宽30Mbps | 满足Web访问和API调用需求 |
| **年费** | 中国移动报价：9676.8 元/年<br>中国电信报价：10236 元/年<br>阿里云报价：37433.21 元/年 | 包含服务器、存储和带宽费用 |

### 4.2. 服务器部署

* **生产环境**：1台服务器，同时运行Web应用和数据库
* **测试环境**：使用开发人员本地环境或虚拟机
* **开发环境**：开发人员本地环境

---

## 5. 网络架构设计

### 5.1. 网络拓扑

```
[用户] --> [Web服务器] --> [数据库(同一服务器)]
```

### 5.2. 网络安全方案

* **防火墙配置**：限制只开放必要端口
* **HTTPS加密**：所有外部通信采用HTTPS加密
* **访问控制**：基于角色的访问控制系统
* **安全更新**：定期应用系统安全更新

---

## 6. 存储方案设计

### 6.1. 数据库存储

* **数据库类型**：MySQL 8.0 + MongoDB 6.0
* **数据库用途**：
  * MySQL：存储业务数据（用户信息、预警记录等）
  * MongoDB：存储GEO矢量数据（地质灾害点和风险防范区的面类型矢量数据）
* **存储位置**：与应用部署在同一服务器
* **存储容量**：分配200GB空间用于数据库
* **备份策略**：每日自动备份，保留7天

### 6.2. 文件存储

* **存储类型**：本地文件系统
* **存储容量**：分配200GB空间用于文件存储
* **访问控制**：基于角色的访问权限控制
* **备份策略**：定期备份重要文件

---

## 7. 技术栈配置

### 7.1. 应用技术栈

* **后端框架**：Python FastAPI
* **前端框架**：Vue3 + Element Plus
* **构建工具**：Vite
* **Web服务器**：Nginx

### 7.2. 数据处理技术

* **SHP数据处理**：删除涉密数据后导入数据库
* **矢量数据处理**：支持面类型几何数据处理
* **地图显示规范**：按地质灾害规范颜色显示图层

---

## 8. 第三方服务集成

### 7.1. 地图服务

* **服务提供商**：天地图
* **API类型**：Web API
* **使用场景**：地质灾害点和风险防范区可视化展示

### 7.2. 消息服务

* **短信服务**：运营商短信服务
* **微信公众号**：茂名市自然资源局公众号
* **使用场景**：预警信息发布、用户通知

---

## 8. 系统监控与运维

### 8.1. 监控方案

* **服务器监控**：CPU、内存、磁盘、网络等基础指标
* **应用监控**：响应时间、错误率、并发数等应用指标
* **日志管理**：本地日志收集和分析
* **告警机制**：邮件告警

### 8.2. 运维工具

* **部署工具**：Docker + Docker Compose
* **版本控制**：Git
* **配置管理**：环境变量和配置文件

---

## 9. 灾备与扩展性

### 9.1. 灾备方案

* **数据备份**：数据库和重要文件定期备份
* **应用备份**：定期备份应用代码和配置
* **恢复方案**：制定详细的灾难恢复流程

### 9.2. 扩展性规划

* **垂直扩展**：根据需要提升单机配置
* **未来升级**：预留向分布式架构升级的可能性
* **模块化设计**：系统采用模块化设计，便于未来扩展

---

## 10. 成本估算

### 10.1. 初始投资

* **服务器成本**：9676.8元 至 37433.21 元/年
* **第三方服务**：短信服务约76.73万至85.9万元/年（基于中国移动报价）、约192万至279万元/年（基于中国电信报价）

> 基于2019年以来茂名市自然灾害（台风、暴雨、泥石流、山体滑坡等）发生数据采用三点估算法计算，预计每年保守情况下发送短信12788248条、积极情况下发送短信18625130条、最可能发送短信14316416条。经调研中国移动和中国电信的短信价格，中国移动报价0.06元/条，中国电信报价0.15元/条。

---

## 11. 附录

### 11.1. 参考资料

* 阿里云ECS产品文档
* Ubuntu 24.04 LTS官方文档
* FastAPI部署最佳实践
* MySQL 8.0官方文档
* MongoDB官方文档

### 11.2. 术语表

* **ECS**：弹性计算服务
* **ESSD**：增强型SSD
* **PL**：性能级别
* **IOPS**：每秒输入/输出操作数
* **FastAPI**：Python高性能Web框架
