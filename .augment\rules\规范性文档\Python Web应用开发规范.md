---
type: "manual"
---

# Python Web 应用开发规范

## 文档信息

| 项目 | 内容 |
|---|---|
| 文档名称 | Python Web 应用开发规范 |
| 文档版本 | v1.0.0 |
| 生效日期 | 2025-06-24 |
| 适用范围 | 所有基于 FastAPI 框架的 Python Web 应用 |
| 审批人 | 技术负责人 |
| 维护人 | 研发团队 |
| 更新频率 | 季度评审 |

## 目录

1. [概述和适用范围](#1-概述和适用范围)
2. [核心技术栈](#2-核心技术栈)
3. [基础编码规范 (PEP 8)](#3-基础编码规范-pep-8)
4. [代码质量工具配置](#4-代码质量工具配置)
5. [项目结构和模块化](#5-项目结构和模块化)
6. [FastAPI 核心实践](#6-fastapi-核心实践)
7. [文档和注释规范](#7-文档和注释规范)
8. [类型提示规范](#8-类型提示规范)
9. [配置管理规范](#9-配置管理规范)
10. [日志记录规范](#10-日志记录规范)
11. [错误处理规范](#11-错误处理规范)
12. [单元测试规范](#12-单元测试规范)
13. [安全编码规范](#13-安全编码规范)
14. [Git工作流规范](#14-git工作流规范)
15. [CI/CD集成规范](#15-cicd集成规范)
16. [附录：配置文件示例](#16-附录配置文件示例)

## 1. 概述和适用范围

### 1.1 目标
本规范旨在为基于 FastAPI 框架的 Python Web 应用提供一套统一的开发标准，以实现：
- 提升API接口质量和可维护性
- 统一项目结构，促进团队协作
- 强化类型安全和数据验证
- 规范化开发流程，提升效率

### 1.2 适用范围
- **【强制】** 所有新开发的 Python Web 应用项目必须采用 FastAPI 框架并遵循本规范。
- 现有项目的重构和重要更新。

## 2. 核心技术栈

- **Web 框架**: **FastAPI**
- **数据验证与设置管理**: **Pydantic**
- **ORM (对象关系映射)**: **SQLModel** (推荐) 或 **SQLAlchemy**
- **数据库**: **PostgreSQL**, **MySQL**
- **异步库**: **httpx** (用于异步客户端请求), **asyncpg** (用于异步 PostgreSQL 驱动)

## 3. 基础编码规范 (PEP 8)

严格遵循《Python 项目编码规范》中定义的 **PEP 8** 基础规范。所有代码格式化、命名约定、代码布局等均需符合该规范。

## 4. 代码质量工具配置

所有项目必须集成 `black`, `flake8`, `isort`, `mypy` 等代码质量工具。配置应写入项目根目录的 `pyproject.toml` 文件中。配置细节请参考《Python 项目编码规范》的第 3 节和第 20 节。

## 5. 项目结构和模块化

为保证项目的一致性和可维护性，所有 FastAPI 项目应遵循以下标准目录结构。该结构通过在 API 层强制实施版本化，并分离不同层级的关注点，实现了高内聚、低耦合。

```
your-project/
├── app/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── deps.py             # 全局 API 依赖项 (如: get_current_user)
│   │   └── v1/
│   │       ├── __init__.py
│   │       ├── api_router.py   # 聚合 v1 版本的所有路由
│   │       └── endpoints/
│   │           ├── __init__.py
│   │           ├── items.py    # 'items' 功能模块的路由
│   │           └── users.py    # 'users' 功能模块的路由
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py           # 配置管理 (使用 Pydantic BaseSettings)
│   │   └── security.py         # 安全相关 (密码哈希, JWT)
│   ├── crud/
│   │   ├── __init__.py
│   │   ├── base.py             # 基础 CRUD 操作类
│   │   ├── crud_item.py        # 'items' 功能模块的 CRUD
│   │   └── crud_user.py        # 'users' 功能模块的 CRUD
│   ├── db/
│   │   ├── __init__.py
│   │   ├── base.py             # 声明式 Base 和元数据
│   │   └── session.py          # 数据库会话管理
│   ├── models/
│   │   ├── __init__.py
│   │   ├── item.py             # 'items' 的数据库模型 (SQLModel)
│   │   └── user.py             # 'users' 的数据库模型 (SQLModel)
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── item.py             # 'items' 的 Pydantic Schema
│   │   ├── user.py             # 'users' 的 Pydantic Schema
│   │   └── token.py            # JWT Token Schema
│   └── main.py                 # FastAPI 应用实例, 挂载版本化的 API 路由
├── tests/                      # 测试目录
│   ├── __init__.py
│   ├── conftest.py             # Pytest 配置文件和全局 Fixtures
│   ├── api/
│   │   └── v1/
│   │       └── endpoints/
│   │           └── test_users.py
│   └── crud/
│       └── test_crud_item.py
├── .env.example                # 环境变量示例文件
├── .gitignore
├── pyproject.toml              # 项目配置文件 (依赖, 工具配置)
└── README.md
```

## 6. FastAPI 核心实践

### 6.1 API 路由和版本化

- **【强制】版本化管理**: 所有API必须进行版本化管理。版本号必须体现在URL路径中，例如 `/api/v1/...`。新项目应从 `v1` 开始。
- **【强制】路由分离**: 每个功能模块（如 users, items）的路由应定义在对应版本目录的 `endpoints/` 子目录下的独立文件中，例如 `app/api/v1/endpoints/items.py`。
- **【强制】路由聚合**: 在每个版本目录下（如 `app/api/v1/`），应有一个 `api_router.py` 文件。该文件负责创建一个 `APIRouter` 实例，并使用 `include_router` 聚合该版本下 `endpoints/` 目录中所有的功能模块路由。
- **【强制】主应用集成**: 在 `app/main.py` 中，通过 `app.include_router()` 将每个版本的聚合路由（例如 `v1` 的 `api_router`）挂载到对应的版本前缀下。
- **APIRouter**: 使用 `fastapi.APIRouter` 来组织各功能模块的路由。
- **路径操作装饰器**: 使用 `@router.get`, `@router.post` 等装饰器定义端点。
- **路径参数**: 使用 f-string 风格的路径参数，如 `@router.get("/items/{item_id}")`。

### 6.2 Pydantic 模型 (Schemas)

- **【强制】** 所有API端点的数据输入（请求体）和输出（响应体）都必须使用 Pydantic 模型进行定义和验证。
- **文件位置**: Pydantic 模型应定义在 `app/schemas/` 目录下，按功能模块分离。
- **模型分离**: 为创建、更新和读取操作定义不同的模型，以精确控制字段的读写权限。
  - `ItemBase`: 包含通用字段。
  - `ItemCreate(ItemBase)`: 创建时需要的字段。
  - `ItemUpdate(ItemBase)`: 更新时允许修改的字段。
  - `ItemInDB(ItemBase)`: 存储在数据库中的模型。
  - `Item(ItemInDB)`: 从 API 返回给用户的模型。

### 6.3 依赖注入 (Dependency Injection)

- **广泛使用**: 依赖注入是 FastAPI 的核心特性，应广泛用于处理数据库会话、用户认证、权限校验等共享逻辑。
- **数据库会话**: 定义一个 `get_db` 依赖项 (在 `app/db/session.py`) 来管理数据库会话的生命周期，确保每个请求使用独立的会话，并在请求结束后关闭。
- **认证与安全**: 定义 `get_current_user` 等依赖项 (在 `app/api/deps.py`) 来保护需要认证的端点。
- **可复用性**: 将通用依赖项定义在 `app/api/deps.py` 中，供不同路由使用。

### 6.4 数据库交互 (CRUD/Services)

- **【强制】** 禁止在 API 路由函数中直接编写数据库查询和操作逻辑。
- **CRUD 层**: 所有数据库操作逻辑应封装在 `app/crud/` 目录下的函数或类中。
- **通用基类**: 可以创建一个 `BaseCRUD` 类 (在 `app/crud/base.py`)，实现通用的增删改查方法，各个模块的 CRUD 类继承自它。
- **异步操作**: 如果使用异步数据库驱动（如 `asyncpg`），则 CRUD 函数必须是 `async def`。

### 6.5 错误处理

- **HTTPException**: 对于预期的客户端错误（如资源未找到、权限不足），应主动抛出 `fastapi.HTTPException`，并提供清晰的 `status_code` 和 `detail` 信息。
- **自定义异常处理器**: 对于应用级的自定义异常，或需要统一处理某类第三方库异常的情况，使用 `@app.exception_handler()` 装饰器注册自定义异常处理器。
- **验证错误**: FastAPI 会自动处理 Pydantic 的验证错误，并返回 422 状态码和详细的错误信息。

## 7. 文档和注释规范

遵循《Python 项目编码规范》中定义的 **Google Python Style Docstrings** 格式。所有公共模块、函数、类及其方法都必须包含文档字符串。

## 8. 类型提示规范

**【强制】** 严格遵循《Python 项目编码规范》中的类型提示要求。所有函数签名、Pydantic 模型和 SQLModel 模型都必须有明确的类型注解。

## 9. 配置管理规范

- **环境变量**: **【强制】** 使用环境变量管理所有敏感信息（数据库连接字符串、API密钥、JWT密钥等）和环境相关配置。
- **Pydantic `BaseSettings`**: 使用 Pydantic 的 `BaseSettings` 类在 `app/core/config.py` 中定义配置模型，它可以自动从环境变量和 `.env` 文件中读取配置。
- **`.env` 文件**: 项目根目录应包含一个 `.env.example` 文件作为模板，但不应将包含敏感信息的 `.env` 文件提交到版本控制中。

## 10. 日志记录规范

- **统一配置**: 使用 `logging.config` 和 YAML 或 JSON 配置文件来统一管理日志。
- **Uvicorn 日志**: 配置 Uvicorn 的访问日志格式，以包含请求ID等关键信息，便于追踪。
- **应用日志**: 在应用的 `main.py` 或 `core/config.py` 中设置应用日志，记录关键业务逻辑、错误和调试信息。**禁止使用 `print()`**。

## 11. 错误处理规范
遵循《Python 项目编码规范》中定义的错误处理规范。对于 Web 应用，尤其要善用 **HTTPException** 和 **自定义异常处理器**。

## 12. 单元测试规范

- **【强制】** 使用 `pytest` 作为测试框架。更多关于单元测试的核心原则、模式（如AAA）、Mocking 等通用实践，请严格遵循团队的 **《单元测试编写指南.md》**。
- **HTTP 客户端**: 使用 FastAPI 提供的 `TestClient` 进行同步测试，或 `httpx.AsyncClient` 进行异步测试。

### 12.1 测试结构
- **【强制】** 测试代码的目录结构必须镜像 `app` 目录的结构。
- 测试文件名必须以 `test_` 开头，测试函数名也必须以 `test_` 开头。

**针对 FastAPI 版本化 API 的结构示例:**
```
your_fastapi_project/
├── app/
│   └── api/
│       └── v1/
│           └── endpoints/
│               └── users.py
└── tests/
    └── api/
        └── v1/
            └── endpoints/
                └── test_users.py
```

### 12.2 数据库与依赖隔离
- **【强制】** 测试必须在独立的测试数据库中运行，严禁连接生产或开发数据库。
- **【强制】** 使用 `app.dependency_overrides` 在测试期间将 `get_db` 等外部依赖替换为返回测试实例（如测试数据库会话）的新依赖。

### 12.3 核心 Fixtures 与 `conftest.py`
- **【强制】** 所有全局共享的、用于测试设置（如 `TestClient` 实例化、数据库依赖覆盖）的 Fixtures 必须定义在 `tests/conftest.py` 文件中。
- **核心 Fixtures 示例**:
```python
# tests/conftest.py 示例
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 假设的被测应用和依赖
from app.main import app
from app.db.session import Base
from app.api.v1.deps import get_db # 假设依赖在版本化目录下

# 1. 设置独立的测试数据库
TEST_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 2. 使用 Fixture 管理数据库表的创建和销毁 (session 作用域)
@pytest.fixture(scope="session", autouse=True)
def setup_test_db():
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

# 3. 定义 Fixture 来覆盖 get_db 依赖
@pytest.fixture(scope="function")
def db_session_override():
    def override_get_db():
        try:
            db = TestingSessionLocal()
            yield db
        finally:
            db.close()

    app.dependency_overrides[get_db] = override_get_db
    yield
    app.dependency_overrides.clear()

# 4. 创建可供所有测试使用的 TestClient Fixture
@pytest.fixture(scope="module")
def client() -> TestClient:
    return TestClient(app)
```

## 13. 安全编码规范

除遵循《Python 项目编码规范》中的通用安全准则外，Web 应用需特别注意：
- **认证**: **【强制】** 使用 **OAuth2 with Password Flow and Bearer Tokens** (由 FastAPI 内置支持) 进行用户认证。
- **密码哈希**: **【强制】** 密码必须使用强哈希算法（如 `passlib` 库支持的 `bcrypt`）进行存储。
- **跨域资源共享 (CORS)**: 使用 FastAPI 的 `CORSMiddleware` 明确配置允许的源、方法和头。不要使用通配符 `*` 允许所有源。
- **依赖安全**: 定期使用 `safety` 或类似工具扫描项目依赖，检查是否存在已知漏洞。

## 14. Git工作流规范

遵循《Python 项目编码规范》中定义的 **Git 工作流** 和 **Conventional Commits** 提交消息规范。

## 15. CI/CD集成规范

遵循《Python 项目编码规范》中定义的 CI/CD 集成规范。CI 流水线中必须包含代码质量检查、类型检查和单元测试步骤。

## 16. 附录：配置文件示例

### 16.1 pyproject.toml
```toml
[tool.poetry]
name = "your-fastapi-project"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.10"
fastapi = "^0.95.0"
uvicorn = {extras = ["standard"], version = "^0.21.0"}
pydantic = {extras = ["email"], version = "^1.10.7"}
sqlmodel = "^0.0.8"
psycopg2-binary = "^2.9.5" # For PostgreSQL
python-dotenv = "^1.0.0"
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-jose = {extras = ["cryptography"], version = "^3.3.0"}

[tool.poetry.group.dev.dependencies]
pytest = "^7.3.1"
pytest-cov = "^4.0.0"
black = "^23.3.0"
flake8 = "^6.0.0"
mypy = "^1.2.0"
isort = "^5.12.0"
httpx = "^0.24.0" # For TestClient

# ... isort, black, mypy configurations ...
```

### 16.2 .env.example
```
# PostgreSQL
POSTGRES_SERVER=localhost
POSTGRES_USER=youruser
POSTGRES_PASSWORD=yourpassword
POSTGRES_DB=yourdb
DATABASE_URL=postgresql://youruser:yourpassword@localhost/yourdb

# JWT
SECRET_KEY=your_super_secret_key
ACCESS_TOKEN_EXPIRE_MINUTES=30
```