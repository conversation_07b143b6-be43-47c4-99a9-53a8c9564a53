# 视觉风格规范与关键界面概念稿

---

## 标准文档页眉

| 属性 | 值 |
| :--- | :--- |
| **项目名称** | 茂名市地质灾害预警平台 |
| **文档标题** | 视觉风格规范与关键界面概念稿 |
| **文档版本** | 1.0 |
| **创建日期** | 2025-07-11 |
| **AI助手** | Augment Agent (开发者: Augment Code) |
| **用户代表** | 梁铭显 |
| **依据文档** | 《设计风格提案》V1.0、《界面清单与交互流程图》V1.0 |
| **设计风格** | 现代政务简约风 + 科技安全感知风（融合A+B方案） |

---

## 1. 色彩系统

### 1.1 公众查询界面色彩（明亮主题）

**主色调**
- 主蓝色：#1E40AF（政府权威蓝）
- 辅助蓝：#3B82F6（交互蓝）
- 浅蓝色：#DBEAFE（背景蓝）

**功能色彩**
- 成功绿：#10B981
- 警告橙：#F59E0B
- 危险红：#EF4444
- 信息蓝：#3B82F6

**中性色彩**
- 主背景：#FFFFFF（纯白）
- 次背景：#F9FAFB（浅灰）
- 边框色：#E5E7EB
- 主文字：#111827（深灰）
- 次文字：#6B7280（中灰）
- 辅助文字：#9CA3AF（浅灰）

### 1.2 管理后台色彩（暗黑主题）

**主色调**
- 主蓝色：#3B82F6（科技蓝）
- 辅助蓝：#1E40AF（深蓝）
- 科技青：#06B6D4（点缀色）

**功能色彩**
- 成功绿：#10B981
- 警告橙：#F59E0B（安全监测色）
- 危险红：#EF4444
- 信息蓝：#3B82F6

**中性色彩**
- 主背景：#0F172A（深蓝黑）
- 次背景：#1E293B（中深灰）
- 卡片背景：#334155（浅深灰）
- 边框色：#475569
- 主文字：#F1F5F9（浅色）
- 次文字：#CBD5E1（中浅色）
- 辅助文字：#94A3B8（灰色）

---

## 2. 字体规范

### 2.1 字体家族
- **主要字体**：-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif
- **备用字体**：'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif
- **等宽字体**：'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace

### 2.2 字阶与应用

**标题层级**
- H1：32px/40px，font-weight: 700，用于页面主标题
- H2：24px/32px，font-weight: 600，用于区块标题
- H3：20px/28px，font-weight: 600，用于卡片标题
- H4：18px/24px，font-weight: 500，用于小节标题

**正文层级**
- Body Large：16px/24px，font-weight: 400，用于重要正文
- Body：14px/20px，font-weight: 400，用于常规正文
- Body Small：12px/16px，font-weight: 400，用于辅助信息

**功能文字**
- Button：14px/20px，font-weight: 500，用于按钮文字
- Caption：12px/16px，font-weight: 400，用于说明文字
- Label：14px/20px，font-weight: 500，用于表单标签

---

## 3. 布局与网格

### 3.1 栅格系统
- **容器最大宽度**：1200px
- **栅格列数**：12列
- **列间距**：24px
- **外边距**：24px（桌面端），16px（移动端）

### 3.2 基础间距单位
- **基础单位**：8px
- **常用间距**：8px, 16px, 24px, 32px, 48px, 64px
- **组件内边距**：8px, 12px, 16px, 24px
- **组件外边距**：16px, 24px, 32px, 48px

### 3.3 断点设置
- **移动端**：< 768px
- **平板端**：768px - 1024px
- **桌面端**：> 1024px

---

## 4. 图标风格

### 4.1 图标库选择
- **主要图标库**：FontAwesome 6.0+
- **图标风格**：线性图标为主，填充图标为辅
- **图标尺寸**：16px, 20px, 24px, 32px

### 4.2 自定义图标规范
- **设计风格**：简洁线性，2px描边
- **圆角处理**：统一2px圆角
- **地质元素**：结合山体、岩石、预警等元素设计

---

## 5. 核心UI组件规范

### 5.1 按钮组件

**主要按钮（Primary Button）**
- 背景色：#1E40AF（公众界面）/ #3B82F6（管理后台）
- 文字色：#FFFFFF
- 圆角：6px
- 内边距：12px 24px
- 字体：14px/20px，font-weight: 500

**交互状态**
- Hover：背景色加深10%，添加阴影
- Pressed：背景色加深20%
- Disabled：背景色#9CA3AF，文字色#6B7280

**次要按钮（Secondary Button）**
- 背景色：透明
- 边框：1px solid #E5E7EB（公众界面）/ #475569（管理后台）
- 文字色：#1E40AF（公众界面）/ #3B82F6（管理后台）

### 5.2 输入框组件

**基础样式**
- 边框：1px solid #E5E7EB（公众界面）/ #475569（管理后台）
- 圆角：6px
- 内边距：12px 16px
- 字体：14px/20px

**交互状态**
- Focus：边框色#3B82F6，添加蓝色阴影
- Error：边框色#EF4444，添加红色阴影
- Disabled：背景色#F9FAFB，文字色#9CA3AF

### 5.3 卡片组件

**基础样式**
- 背景色：#FFFFFF（公众界面）/ #334155（管理后台）
- 圆角：8px
- 阴影：0 1px 3px rgba(0, 0, 0, 0.1)
- 内边距：24px

**交互状态**
- Hover：阴影加深，轻微上移效果

### 5.4 导航栏组件

**公众界面导航**
- 背景色：#FFFFFF
- 高度：64px
- 阴影：0 1px 3px rgba(0, 0, 0, 0.1)
- Logo + 导航菜单 + 用户操作区

**管理后台导航**
- 侧边栏宽度：240px（展开）/ 60px（收起）
- 背景色：#1E293B
- 顶部工具栏高度：64px
- 边框：右侧1px solid #475569

### 5.5 汉堡菜单按钮组件

**基础样式**
- 尺寸：36×36px
- 背景色：#334155
- 边框：1px solid #475569
- 圆角：6px
- 图标色：#CBD5E1

**交互状态**
- Hover：background: #475569, color: #F1F5F9
- Active：background: #64748B, transform: scale(0.95)
- Focus：outline: none（移除默认轮廓线）

**图标状态变化**
- 桌面端展开状态：fa-outdent（收起图标）
- 桌面端收起状态：fa-indent（展开图标）
- 移动端关闭状态：fa-bars（汉堡图标）
- 移动端打开状态：fa-times（关闭图标）

### 5.6 侧边栏导航组件

**基础样式**
- 展开宽度：240px
- 收起宽度：60px
- 背景色：#1E293B
- 边框：右侧1px solid #475569
- 过渡动画：width 0.3s ease

**导航菜单项状态**
- 正常状态：color: #CBD5E1, background: transparent
- 悬停状态：color: #F1F5F9, background: #334155
- 按下状态：background: #475569, transform: scale(0.98)
- 激活状态：color: #3B82F6, background: rgba(59, 130, 246, 0.2), border-right: 3px solid #3B82F6

**移动端遮罩层**
- 背景色：rgba(0, 0, 0, 0.5)
- 过渡动画：opacity 0.3s ease
- 点击行为：关闭侧边栏

### 5.7 表格组件

**基础样式**
- 表头背景：#F9FAFB（公众界面）/ #1E293B（管理后台）
- 行高：48px
- 边框：1px solid #E5E7EB（公众界面）/ #475569（管理后台）
- 斑马纹：奇偶行背景色区分

**交互状态**
- Hover：行背景色高亮
- Selected：行背景色蓝色高亮

### 5.8 侧边抽屉组件

**设计原则**
- 用于复杂表单操作和详细信息展示
- 从页面右侧滑出，保持主界面可见性
- 完全替代传统模态对话框，提供现代化交互体验

**基础样式**
- 位置：从页面右侧滑出
- 宽度：500px（桌面端）/ 450px（平板端）/ 90%屏幕宽度且最大450px（移动端）
- 高度：100vh（全屏高度）
- 背景：#1e293b（管理后台暗色主题）
- 边框：左侧1px solid #475569
- 阴影：-4px 0 15px rgba(0, 0, 0, 0.3)

**结构组成**
- 抽屉头部：64px高度，包含标题和关闭按钮，sticky定位
- 抽屉主体：可滚动内容区域，内边距24px
- 抽屉底部：操作按钮区域，右对齐，sticky定位

**动画效果**
- 进入动画：transform: translateX(0)，0.3s ease
- 退出动画：transform: translateX(100%)，0.3s ease
- 遮罩层：rgba(0, 0, 0, 0.5)，同步淡入淡出

**交互行为**
- 打开方式：点击"新增"、"编辑"、"查看"等操作按钮
- 关闭方式：点击关闭按钮、点击遮罩层、按ESC键、操作完成后自动关闭

### 5.9 Toast通知组件

**设计原则**
- 完全替代传统alert和confirm弹窗
- 提供现代化的用户反馈体验
- 支持多种通知类型和自动消失机制

**基础样式**
- 位置：页面右上角，fixed定位
- 容器位置：top: 80px, right: 20px
- 最大宽度：400px，最小宽度：320px
- 背景：#1e293b
- 边框：1px solid #475569
- 圆角：8px
- 阴影：0 10px 25px rgba(0, 0, 0, 0.3)

**通知类型**
- 成功通知（success）：绿色#10b981，图标fas fa-check
- 错误通知（error）：红色#ef4444，图标fas fa-times
- 警告通知（warning）：橙色#f59e0b，图标fas fa-exclamation
- 信息通知（info）：蓝色#3b82f6，图标fas fa-info

**视觉设计**
- 左边框：4px solid [对应类型颜色]
- 图标区域：20px圆形背景，对应颜色20%透明度
- 标题文字：#f1f5f9，14px，font-weight: 600
- 消息文字：#cbd5e1，13px，line-height: 1.4

**动画效果**
- 进入：从右侧滑入，transform: translateX(0)，0.3s ease
- 退出：向右侧滑出，transform: translateX(100%)，0.3s ease
- 自动消失：4秒后自动隐藏
- 堆叠：支持多个通知同时显示，垂直排列

**交互行为**
- 手动关闭：点击右上角关闭按钮
- 自动消失：4秒后自动隐藏
- 堆叠管理：新通知在上方显示，旧通知向下移动

### 5.10 确认对话框组件

**设计原则**
- 替代原生confirm，提供与整体设计一致的确认体验
- 用于重要操作的二次确认，如删除、重置等

**基础样式**
- 位置：屏幕中央，fixed定位
- 最大宽度：400px，宽度90%（移动端适配）
- 背景：#1e293b
- 圆角：12px
- 阴影：0 20px 40px rgba(0, 0, 0, 0.4)
- 遮罩：rgba(0, 0, 0, 0.6)

**结构组成**
- 图标：40px圆形，警告图标，红色背景rgba(239, 68, 68, 0.2)
- 标题：18px，font-weight: 600，#f1f5f9
- 消息：14px，line-height: 1.5，#cbd5e1
- 按钮：取消（次要按钮）+ 确认（危险按钮）

**动画效果**
- 进入：缩放动画，scale(0.9) → scale(1)，0.3s ease
- 退出：淡出效果，opacity: 1 → 0，0.3s ease

**交互行为**
- 键盘支持：ESC键取消
- 异步支持：使用Promise处理用户选择
- 自动聚焦：确认按钮获得焦点

---

## 6. 公共界面架构规范

### 6.1 管理后台统一架构（强制遵循）

**整体布局结构**
```
┌─────────────────────────────────────────────────────────┐
│ 左侧导航栏 (240px/60px) │ 主内容区 (flex: 1)            │
│                        ├─────────────────────────────────┤
│ - Logo & 标题           │ 顶部工具栏 (64px)              │
│ - 导航菜单组            │ ├─ 汉堡菜单 + 面包屑 + 用户菜单 │
│                        ├─────────────────────────────────┤
│                        │ 页面内容区 (flex: 1)           │
│                        │ └─ 各页面自定义内容             │
└─────────────────────────────────────────────────────────┘
```

**必须遵循的公共组件**：

1. **侧边栏组件**
   - 展开宽度：240px
   - 收起宽度：60px
   - 背景色：#1E293B
   - 边框：右侧1px solid #475569
   - 过渡动画：width 0.3s ease

2. **顶部工具栏组件**
   - 高度：64px
   - 背景色：#1E293B
   - 边框：底部1px solid #475569
   - 左侧：汉堡菜单按钮（36×36px）
   - 中间：面包屑导航
   - 右侧：通知 + 用户菜单

3. **汉堡菜单按钮**
   - 尺寸：36×36px
   - 背景色：#334155
   - 边框：1px solid #475569
   - 圆角：6px
   - 图标智能变化：fa-bars/fa-times/fa-indent/fa-outdent

4. **导航菜单项**
   - 正常状态：color: #CBD5E1, background: transparent
   - 悬停状态：color: #F1F5F9, background: #334155
   - 按下状态：background: #475569, transform: scale(0.98)
   - 激活状态：color: #3B82F6, background: rgba(59, 130, 246, 0.2), border-right: 3px solid #3B82F6

**响应式断点**：
- 桌面端（>1024px）：侧边栏默认展开
- 平板端（768px-1024px）：侧边栏自动收起
- 移动端（≤768px）：侧边栏隐藏，通过汉堡菜单滑出

### 6.2 公众界面统一架构（强制遵循）

**整体布局结构**
```
┌─────────────────────────────────────────────────────────┐
│ 顶部导航栏 (64px)                                       │
│ ├─ Logo + 标题 + 导航菜单 + 用户操作                     │
├─────────────────────────────────────────────────────────┤
│ 主内容区 (flex: 1)                                      │
│ └─ 各页面自定义内容                                      │
└─────────────────────────────────────────────────────────┘
```

**必须遵循的公共组件**：

1. **顶部导航栏（必须统一）**
   - 高度：64px
   - 背景色：#FFFFFF
   - 阴影：0 1px 3px rgba(0, 0, 0, 0.1)
   - 左侧：Logo + 平台标题
   - 右侧：导航菜单 + 用户操作区

2. **色彩系统（必须遵循）**
   - 主色调：#1E40AF（政府权威蓝）
   - 辅助色：#3B82F6（交互蓝）
   - 背景色：#FFFFFF（纯白）

### 6.3 架构扩展规则

**允许自定义的部分**：
- 页面内容区的具体布局和组件
- 页面特有的功能组件和交互
- 页面级别的数据展示方式

**禁止修改的部分**：
- 侧边栏的基础样式和交互逻辑
- 顶部工具栏的基础结构和样式
- 汉堡菜单的图标变化逻辑
- 响应式断点和行为
- 公共组件的色彩和尺寸规范

---

## 7. 关键界面概念稿

### 6.1 公众查询主界面（P001）

**界面名称**：首页/查询主界面

**设计说明**：
采用明亮主题的现代政务简约风格，融入适量科技元素。界面以地图为核心，提供清晰的查询入口和筛选工具。

**布局结构**：
- 顶部导航栏：Logo + 标题（简化设计，移除导航菜单和帮助链接）
- 主要内容区：
  - 左侧：查询工具面板（区域筛选） + 地图图例
  - 右侧：天地图展示区域（卫星影像底图 + 地质灾害点 + 风险防范区）
  - 统计信息：简洁卡片，浮动在地图左下角
- 底部：版权信息 + 联系方式

**关键设计点**：
- 地图占据主要视觉空间，使用天地图卫星影像底图
- 导航栏简化设计，突出核心功能
- 地质灾害点：中心标记 + 100米蓝色影响范围圆圈
  - **影响范围颜色规范**：统一使用蓝色(#3498db)，透明度0.2，边框透明度0.8，边框宽度2px
  - **设计理由**：统一的蓝色标识便于用户识别地质灾害点的影响范围，避免与风险等级颜色混淆
- 风险防范区：不规则面数据，按风险等级使用不同颜色（高风险红色、中风险橙色、低风险绿色）
- 统计信息采用紧凑设计，减少空间占用

### 6.2 系统管理仪表板（M002）

**界面名称**：系统主界面/仪表板

**设计说明**：
采用暗黑主题的科技感知风格，体现专业监测系统的特点。通过数据卡片和图表展示系统概览信息。

**布局结构**：
- 左侧导航栏：折叠式菜单，图标+文字
- 顶部工具栏：面包屑导航 + 用户信息 + 通知
- 主要内容区：
  - 数据统计卡片组（4个关键指标）
  - 快捷操作区域
  - 系统状态监控面板
  - 最近操作日志列表

**关键设计点**：
- 深蓝黑背景营造专业科技感
- 数据卡片使用玻璃拟态效果，增强视觉层次
- 橙色用于重要数据和警示信息
- 图表采用蓝色系配色，保持视觉一致性

### 6.3 移动端查询界面（P002）

**界面名称**：移动端查询界面

**设计说明**：
基于公众查询界面的移动端优化版本，采用标准的移动端双层结构（header-content），提供GPS定位功能和抽屉式预警信息列表，提供触屏友好的交互设计。

**页面结构**：
- **mobile-header**：顶部导航区域（88px）
  - 状态栏模拟（44px）：信号强度、运营商、电池电量
  - 导航栏（44px）：茂名市地质灾害预警平台标题
- **mobile-content**：主要内容区域（flex: 1，自动占用剩余空间）
  - 全屏地图展示（天地图卫星影像底图）
  - 右侧图层控制按钮：灾害点开关、防范区开关、预警信息开关（48×48px）
  - 右下角我的位置按钮：GPS定位功能（48×48px）

**关键设计点**：
- 标准移动端双层结构，符合现代应用设计规范
- 19.5:9屏幕比例（375×720px），适配主流智能手机
- 真实状态栏模拟，提供沉浸式移动端体验
- 右侧图层控制按钮，采用P001工具按钮设计风格（48×48px白色半透明）
- 我的位置按钮位于右下角，方便用户单手操作
- 地图点击智能偏移（向右+0.002经度，向下-0.002纬度），避免浮动按钮遮挡
- 集成天地图WMTS瓦片服务，提供真实地理数据展示

### 5.4 预警信息组件

**预警等级色彩**
- 红色预警（1级）：#EF4444
- 橙色预警（2级）：#F59E0B
- 黄色预警（3级）：#EAB308
- 蓝色预警（4级）：#3B82F6

**预警列表项**
- 高度：72px
- 内边距：12px 16px
- 背景色：#FFFFFF
- 边框：1px solid #E5E7EB
- 圆角：6px
- 间距：8px

**预警等级标识**
- 尺寸：8px × 8px
- 形状：圆形
- 位置：左侧对齐

**预警详情对话框**
- 最大宽度：600px
- 背景色：#FFFFFF
- 圆角：12px
- 阴影：0 20px 25px -5px rgba(0, 0, 0, 0.1)
- 遮罩：rgba(0, 0, 0, 0.5)

**镇街标签**
- 背景色：#DBEAFE
- 文字色：#1E40AF
- 内边距：4px 8px
- 圆角：4px
- 可点击状态：hover时背景色变为#3B82F6，文字色变为#FFFFFF

---

## 标准用户审核模块

- **用户意见：** [留空待用户填写]
- **确认状态：** [ ] 待确认 [ ] 已确认
- **确认日期：** YYYY-MM-DD
- **用户签名/确认记录：** [留空待用户处理或记录用户明确同意的文本]
