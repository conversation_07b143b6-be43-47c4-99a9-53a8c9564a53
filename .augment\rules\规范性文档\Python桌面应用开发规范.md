---
type: "manual"
---

# Python 桌面应用开发规范

## 1. 文档信息

| 属性 | 内容 |
|---|---|
| 文档名称 | Python 桌面应用开发规范 |
| 文档版本 | v1.0.0 |
| 生效日期 | 2025-06-24 |
| 适用范围 | 所有Python桌面应用项目 |
| 核心依赖 | [编码规范.md](./编码规范.md), [单元测试编写指南.md](./单元测试编写指南.md) |
| 维护人 | 研发团队 |

## 2. 目录

1. [概述和适用范围](#1-概述和适用范围)
2. [通用编码与质量规范](#2-通用编码与质量规范)
3. [项目结构规范](#3-项目结构规范)
4. [GUI 开发规范 (PyQt)](#4-gui-开发规范-pyqt)
5. [命令行 (CLI) 开发规范](#5-命令行-cli-开发规范)
6. [配置、日志与错误处理](#6-配置日志与错误处理)
7. [单元测试规范](#7-单元测试规范)
8. [打包与分发规范](#8-打包与分发规范)
9. [附录](#9-附录)

---

## 1. 概述和适用范围

### 1.1 目标
本规范旨在为 Python 桌面应用程序的开发提供一套统一的标准和最佳实践，以实现：
- 提高应用程序的代码质量、稳定性和可维护性。
- 确保图形化 (GUI) 和命令行 (CLI) 应用的用户体验一致性。
- 规范从开发、测试到打包分发的完整生命周期。
- 促进团队协作效率，降低项目维护成本。

### 1.2 适用范围
- 所有新开发的 Python 桌面应用程序。
- 涵盖命令行工具 (CLI) 和图形用户界面 (GUI) 应用。

### 1.3 核心框架要求
- **【强制】GUI 应用**: 所有图形化界面应用必须基于 **PyQt6** 框架进行开发。
- **【推荐】CLI 应用**: 推荐使用 **Click** 框架进行命令行应用开发，对于简单脚本可使用 `argparse`。

## 2. 通用编码与质量规范

### 2.1 基础规范
**【强制】** 所有桌面应用项目的代码必须严格遵守 **[编码规范.md](./编码规范.md)** 中定义的所有规则。这是本规范的基础。

### 2.2 代码质量工具
**【强制】** 必须在项目中配置并使用 `black`, `flake8`, `mypy`, `isort` 等代码质量工具，确保代码提交前通过所有检查。

### 2.3 文档与注释
**【强制】** 必须遵循 **[编码规范.md](./编码规范.md)** 中定义的 Google 风格 Docstrings 规范，为所有公共模块、类和函数提供清晰的中文文档。

### 2.4 依赖管理
**【强制】** 所有项目依赖必须在 `pyproject.toml` 和 `requirements.txt` 中明确声明并固定版本。

## 3. 项目结构规范

### 3.1 通用原则
- 遵循 **src-layout** 结构，将所有可安装的源代码放置在 `src/` 目录下。
- 遵循**高内聚、低耦合**原则，按功能逻辑划分模块。

### 3.2 命令行 (CLI) 应用项目结构
```
your_cli_project/
├── src/
│   └── your_cli_app/
│       ├── __init__.py
│       ├── main.py         # 核心逻辑与命令定义 (Click)
│       ├── _version.py     # 版本信息
│       └── utils.py        # 工具函数
├── scripts/
│   └── your_cli_entry.py   # 命令行入口点脚本
├── config/
│   └── config.yaml
├── tests/
│   └── test_main.py
├── pyproject.toml
└── README.md
```

### 3.3 图形化界面 (GUI) 应用项目结构 (PyQt)
```
your_gui_project/
├── src/
│   └── your_gui_app/
│       ├── __init__.py
│       ├── main_window.py      # 主窗口业务逻辑
│       ├── views/
│       │   └── ui_main_window.py # 由 .ui 文件生成
│       ├── core/
│       │   └── business_logic.py # 核心业务逻辑
│       ├── widgets/
│       │   └── custom_widget.py  # 自定义控件
│       └── _version.py         # 版本信息
├── resources/
│   ├── ui/
│   │   └── main_window.ui      # Qt Designer UI 文件
│   └── icons/
│       ├── app.ico
│       └── resource.qrc        # Qt 资源文件
├── main.py                     # 应用程序主入口
├── tests/
│   ├── test_main_window.py
│   └── test_core.py
├── pyproject.toml
└── README.md
```

## 4. GUI 开发规范 (PyQt)

### 4.1 界面与逻辑分离
- **【强制】** 必须使用 **Qt Designer** 设计 UI 界面，并将产物 (`.ui` 文件) 存储在 `resources/ui/` 目录下。
- **【强制】** 必须使用 `pyuic6` 工具将 `.ui` 文件转换为 `.py` 文件，并存储在 `src/your_gui_app/views/` 目录下。禁止手动修改 `pyuic6` 生成的文件。
- **【强制】** 业务逻辑代码 (如事件处理、数据操作) 必须与 UI 定义代码分离。通常通过继承生成的 UI 类来实现。
    ```python
    # 正确示例: src/your_gui_app/main_window.py
    from .views.ui_main_window import Ui_MainWindow
    from PyQt6.QtWidgets import QMainWindow

    class MainWindow(QMainWindow, Ui_MainWindow):
        def __init__(self, parent=None):
            super().__init__(parent)
            self.setupUi(self)  # 设置UI
            self.connect_signals() # 连接信号与槽

        def connect_signals(self):
            self.myButton.clicked.connect(self.on_button_clicked)

        def on_button_clicked(self):
            # 在这里编写业务逻辑
            print("Button was clicked!")
    ```

### 4.2 设计模式
- **【推荐】** 对于复杂应用，推荐采用 **模型-视图-控制器 (MVC)** 或其变体 (如 MVVM, MVP) 来组织代码，彻底分离数据模型 (Model)、用户界面 (View) 和业务逻辑 (Controller)。

### 4.3 资源管理
- **【强制】** 所有静态资源（如图标、图片、翻译文件等）必须通过 Qt 资源系统 (`.qrc` 文件) 进行管理。
- **【强制】** 必须使用 `pyrcc6` 工具编译 `.qrc` 文件为 `.py` 文件，并在代码中通过资源路径 (`:/...`) 引用。禁止在代码中使用相对或绝对文件路径加载资源。

### 4.4 多线程规范
- **【红线】严禁在非 GUI 线程中直接创建、修改或访问任何 `QWidget` 控件。**
- **【强制】** 所有耗时操作（如网络请求、文件读写、复杂计算）必须在单独的 **`QThread`** 中执行，以避免阻塞 UI 线程。
- **【强制】** 工作线程必须通过 **信号与槽 (signals/slots)** 机制与 GUI 线程进行通信，以安全地更新 UI 或传递结果。

### 4.5 样式与主题
- **【推荐】** 使用 QSS (Qt Style Sheets) 对应用程序进行样式化。
- **【推荐】** 将 QSS 代码存储在单独的 `.qss` 文件中，并在程序启动时加载。这有助于实现主题切换和样式维护。

## 5. 命令行 (CLI) 开发规范

### 5.1 框架选择
- **【推荐】** 复杂的 CLI 应用（包含子命令、多选项）应使用 **`Click`** 框架。
- **【允许】** 对于功能单一的简单脚本，可以使用 Python 内置的 **`argparse`** 模块。

### 5.2 接口设计
- **【强制】** 命令、参数和选项的名称必须清晰、直观，并遵循通用惯例 (如 `-v` 表示 verbose, `-o` 表示 output)。
- **【强制】** 必须为所有命令和选项提供有意义的帮助信息 (`--help`)。

### 5.3 输出与交互
- **【强制】** 必须严格区分标准输出 (`stdout`) 和标准错误 (`stderr`)。程序正常执行的输出应写入 `stdout`，所有错误信息、警告和调试信息应写入 `stderr`。
- **【推荐】** 使用 **`rich`** 库来格式化输出，如高亮、表格、进度条等，以提升用户体验。

## 6. 配置、日志与错误处理

### 6.1 配置管理
- 遵循 **[编码规范.md](./编码规范.md)** 中的配置管理原则。
- **【强制】** 用户特定的、可修改的配置文件应存储在用户主目录下的标准位置。
    - **Windows**: `C:/Users/<USER>/AppData/Roaming/YourAppName`
    - **macOS**: `~/Library/Application Support/YourAppName`
    - **Linux**: `~/.config/YourAppName`
- **【推荐】** 使用 `platformdirs` 库来自动获取跨平台的标准目录。

### 6.2 日志记录
- **【强制】** 必须使用 `logging` 模块，并遵循 **[编码规范.md](./编码规范.md)** 中的日志规范。
- **【强制】** 日志文件应存储在用户的数据目录中。
    - **Windows**: `C:/Users/<USER>/AppData/Local/YourAppName/logs`
    - **macOS**: `~/Library/Logs/YourAppName`
    - **Linux**: `~/.local/state/YourAppName/logs`

### 6.3 错误处理
- **GUI 应用**:
    - **【红线】** 严禁让未捕获的异常导致程序闪退。必须在顶层设置全局异常钩子 (`sys.excepthook`)。
    - **【强制】** 必须使用 **`QMessageBox`** 或自定义错误对话框，以友好的方式向用户显示错误信息。对话框应包含清晰的错误描述和可能的解决方案。
    - **【强制】** 详细的 Traceback 信息必须记录到日志文件中，而不能直接展示给用户。
- **CLI 应用**:
    - **【强制】** 发生可预见的错误时，必须向 `stderr` 打印清晰、具体的错误信息。
    - **【强制】** 脚本执行失败时，必须以非零状态码退出 (如 `sys.exit(1)`)。

## 7. 单元测试规范

### 7.1 通用测试原则
- **【强制】** 所有测试代码必须严格遵守 **[单元测试编写指南.md](./单元测试编写指南.md)** 的所有规范。

### 7.2 GUI 测试 (PyQt)
- **【强制】** 必须使用 **`pytest-qt`** 插件进行 GUI 测试。
- **【强制】** 必须使用 `qtbot` fixture 来与控件交互和模拟用户操作（如点击按钮、输入文本、选择下拉框）。
    ```python
    def test_button_click(qtbot, main_window):
        # Arrange
        # main_window is a fixture providing an instance of MainWindow

        # Act
        qtbot.mouseClick(main_window.myButton, PyQt6.QtCore.Qt.MouseButton.LeftButton)

        # Assert
        assert main_window.statusLabel.text() == "Success"
    ```
- **【强制】** 测试应验证 UI 状态的变更（如标签文本、控件禁用/启用）和信号是否被正确发射。

### 7.3 CLI 测试
- **【强制】** 必须使用 `pytest` 配合 `Click` 框架内置的 **`CliRunner`**。
- **【强制】** 测试必须覆盖以下方面：
    - 命令行参数和选项的解析是否正确。
    - 程序是否以正确的退出码 (`exit_code`) 结束。
    - `stdout` 和 `stderr` 的输出是否符合预期。
    ```python
    from click.testing import CliRunner
    from your_cli_app.main import cli

    def test_cli_hello_command():
        # Arrange
        runner = CliRunner()

        # Act
        result = runner.invoke(cli, ['hello', '--name', 'World'])

        # Assert
        assert result.exit_code == 0
        assert "Hello, World!" in result.output
    ```

## 8. 打包与分发规范

### 8.1 工具选择
- **【强制】** 项目必须统一使用 **`PyInstaller`** 作为打包工具。

### 8.2 打包配置 (`.spec` 文件)
- **【强制】** 必须创建一个 `.spec` 文件来管理所有打包配置，并将其提交到版本控制系统中。禁止直接通过命令行参数进行复杂的打包操作。
- **【强制】** 必须在 `.spec` 文件的 `datas` 部分清晰地列出所有需要捆绑的非代码文件（如 `.ui`, `.qrc`, `.ico`, `.qss`, 配置文件模板等）。
- **【强制】** 必须在 `hiddenimports` 部分添加 `PyInstaller` 可能未能自动发现的隐藏依赖。

### 8.3 图标与版本信息
- **【强制】** 必须为应用程序可执行文件指定一个清晰的、符合品牌形象的图标 (`.ico` for Windows, `.icns` for macOS)。
- **【推荐】** 使用 `pyinstaller-version-file` 或类似工具为 Windows 可执行文件嵌入详细的版本信息、公司信息和产品信息。

### 8.4 平台兼容性
- **【原则】** 打包工作应在目标平台上进行。例如，Windows 的 `.exe` 文件应在 Windows 环境中打包。
- **【推荐】** 使用 CI/CD (如 GitHub Actions) 建立跨平台的自动化构建和打包流水线。

## 9. 附录

### 9.1 pyproject.toml 示例 (GUI)
```toml
[project]
# ... (与其他项目一致)
dependencies = [
    "PyQt6>=6.6.0",
    "pyqt6-tools",
    "platformdirs>=4.0.0",
    # ... 其他依赖
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-qt>=4.0.0", # GUI测试
    "pytest-cov",
    # ... 其他开发依赖
]
```

### 9.2 .spec 文件示例 (PyInstaller)
```python
#-*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(['main.py'],
             pathex=['E:\\path\\to\\your_gui_project'],
             binaries=[],
             datas=[('resources/ui', 'resources/ui'), ('resources/icons', 'resources/icons')],
             hiddenimports=['pkg_resources.py2_warn'],
             hookspath=[],
             runtime_hooks=[],
             excludes=[],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher,
             noarchive=False)
pyz = PYZ(a.pure, a.zipped_data,
             cipher=block_cipher)
exe = EXE(pyz,
          a.scripts,
          a.binaries,
          a.zipfiles,
          a.datas,
          [],
          name='YourAppName',
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,
          upx_exclude=[],
          runtime_tmpdir=None,
          console=False, # GUI应用设为False，CLI应用设为True
          icon='resources/icons/app.ico') # 指定图标
```

### 9.3 推荐的 .gitignore 补充
```gitignore
# ... (通用Python .gitignore内容)

# PyInstaller
/build/
/dist/
*.spec

# PyQt / UI
*.pyc
*.pyo
/resources/icons/resource_rc.py # 编译后的qrc文件
/src/your_app/views/ui_*.py    # 编译后的ui文件
```