---
type: "manual"
---

# 前端开发规范

## 1. 文档信息

| 属性 | 内容 |
|---|---|
| 文档名称 | 前端开发规范 |
| 文档版本 | v1.0.0 |
| 生效日期 | 2025-06-24 |
| 适用范围 | 所有基于 Vue.js 的前端项目 |
| 核心依赖 | [编码规范.md](./编码规范.md) |
| 维护人 | 研发团队 |

## 2. 目录

1. [概述和适用范围](#1-概述和适用范围)
2. [核心技术栈](#2-核心技术栈)
3. [编码规范 (ESLint + Prettier)](#3-编码规范-eslint--prettier)
4. [命名规范](#4-命名规范)
5. [项目结构和模块化](#5-项目结构和模块化)
6. [Vue.js 最佳实践](#6-vuejs-最佳实践)
7. [TypeScript 最佳实践](#7-typescript-最佳实践)
8. [组件开发规范](#8-组件开发规范)
9. [样式方案 (CSS/SCSS)](#9-样式方案-cssscss)
10. [状态管理规范 (Pinia)](#10-状态管理规范-pinia)
11. [路由管理规范 (Vue Router)](#11-路由管理规范-vue-router)
12. [API 请求规范](#12-api-请求规范)
13. [文档与注释规范](#13-文档与注释规范)
14. [配置与环境管理](#14-配置与环境管理)
15. [错误处理与日志记录](#15-错误处理与日志记录)
16. [性能优化指导](#16-性能优化指导)
17. [安全编码规范](#17-安全编码规范)
18. [单元与组件测试规范](#18-单元与组件测试规范)
19. [Git 工作流与代码审查](#19-git-工作流与代码审查)
20. [CI/CD 集成规范](#20-cicd-集成规范)
21. [附录：配置文件示例](#21-附录配置文件示例)

---

## 1. 概述和适用范围

### 1.1 目标
本规范旨在为基于 Vue.js 技术栈的前端项目提供一套统一的开发标准、最佳实践和工程化方案，以实现：
- 提高代码质量、可读性和可维护性。
- 统一项目结构和编码风格，提升团队协作效率。
- 确保应用性能、稳定性和用户体验。
- 规范化开发流程，降低项目维护成本。

### 1.2 适用范围
- **【强制】** 所有新开发的前端项目。
- 现有项目的重要更新和模块重构。
- 本规范是对 **[编码规范.md](./编码规范.md)** 在前端领域的具体化和补充。

## 2. 核心技术栈

为保证技术统一和生态系统稳定，所有新项目必须基于以下技术栈进行开发：
- **核心框架**: **Vue 3**
- **开发语言**: **TypeScript**
- **构建工具**: **Vite**
- **UI 组件库**: **Element Plus**
- **路由管理**: **Vue Router**
- **状态管理**: **Pinia**
- **HTTP 客户端**: **Axios** (或基于其封装的库)
- **CSS 预处理器**: **SCSS / Sass**

## 3. 编码规范 (ESLint + Prettier)

### 3.1 自动化格式化与检查
- **【强制】** 所有项目必须集成 **ESLint** (代码质量检查) 和 **Prettier** (代码格式化)。
- **【强制】** 使用 `eslint-plugin-vue` 确保 Vue 代码的规范性，使用 `@typescript-eslint/parser` 和 `@typescript-eslint/eslint-plugin` 检查 TypeScript 代码。
- **【强制】** Prettier 负责统一代码风格（如缩进、引号、分号等），ESLint 负责代码质量（如未使用的变量、潜在的逻辑错误）。两者应协同工作，避免规则冲突。
- **【推荐】** 配置 **Husky** 和 **lint-staged**，在代码提交前 (`pre-commit` 钩子) 自动执行格式化和质量检查，确保所有进入版本库的代码都符合规范。

### 3.2 核心格式化规则 (Prettier)
- **缩进**: 使用 2 个空格。
- **引号**: 优先使用单引号 (`'`)。
- **分号**: 句末不加分号。
- **行尾逗号**: 在多行对象或数组的末尾添加逗号 (`trailingComma: 'es5'`)。
- **行长**: 每行代码长度限制在 80-100 个字符之间，推荐 100。

## 4. 命名规范

- **目录名**: 全部小写，多个单词用短横线 `-` 连接 (kebab-case)，例如 `my-components`。
- **组件文件名**: 使用大驼峰命名法 (PascalCase)，例如 `MyComponent.vue`。
- **TS/JS 文件名**: 使用小驼峰命名法 (camelCase)，例如 `useCounter.ts`。
- **变量/函数名**: 使用小驼峰命名法 (camelCase)，例如 `const userName = 'admin'`。
- **常量**: 使用全大写字母和下划线，例如 `const MAX_RETRIES = 3`。
- **CSS 类名**: 使用 BEM (Block Element Modifier) 命名法或短横线连接法 (kebab-case)。

## 5. 项目结构和模块化

所有项目应遵循以下标准的、功能驱动的目录结构：
```
your-project/
├── public/                     # 静态资源，不会被 Webpack 处理
├── src/
│   ├── api/                    # API 请求模块 (按功能划分)
│   │   └── user.ts
│   ├── assets/                 # 需要 Webpack 处理的静态资源
│   │   └── styles/
│   │       ├── _variables.scss # SCSS 变量
│   │       └── index.scss      # 全局样式
│   ├── components/             # 全局可复用组件
│   │   ├── SvgIcon/
│   │   └── Table/
│   ├── config/                 # 项目配置 (非环境)
│   ├── constants/              # 全局常量
│   ├── directives/             # 自定义指令
│   ├── enums/                  # 枚举
│   ├── hooks/                  # 自定义 Composition API (Hooks)
│   │   └── usePagination.ts
│   ├── layouts/                # 布局组件
│   │   └── default/
│   ├── locales/                # i18n 国际化
│   ├── plugins/                # 插件 (如 element-plus, axios)
│   ├── router/                 # 路由配置
│   │   └── index.ts
│   ├── store/                  # Pinia 状态管理
│   │   ├── modules/
│   │   │   └── user.ts
│   │   └── index.ts
│   ├── types/                  # TypeScript 类型定义
│   │   └── global.d.ts
│   ├── utils/                  # 工具函数
│   │   └── request.ts          # Axios 封装
│   ├── views/                  # 页面级组件 (按业务模块划分)
│   │   ├── login/
│   │   │   └── index.vue
│   │   └── user-management/
│   │       ├── components/     # 页面内部组件
│   │       └── index.vue
│   ├── App.vue                 # 根组件
│   └── main.ts                 # 应用入口
├── .env                        # 环境变量
├── .env.development
├── .env.production
├── .eslintrc.cjs
├── .gitignore
├── .prettierrc
├── index.html
├── package.json
├── tsconfig.json
└── vite.config.ts
```

## 6. Vue.js 最佳实践

- **【强制】** 全面使用 **Composition API** 和 **`<script setup>`** 语法糖。这是 Vue 3 的核心，能更好地组织逻辑和代码复用。
- **【强制】** 组件命名应为多个单词，避免与 HTML 标签冲突。例如使用 `UserAvatar` 而不是 `Avatar`。
- **【强制】** 组件的 `props` 必须尽可能详细：定义 `type`、`required`，并提供 `default` 值。对于复杂类型，应使用 `PropType`。
- **【强制】** 使用 `defineEmits` 明确声明组件所有可能触发的自定义事件。
- **【强制】** `v-for` 循环必须提供 `key`，并且 `key` 必须是唯一的、稳定的值（如 `item.id`），避免使用索引 `index`。
- **【强制】** `v-for` 和 `v-if` 禁止在同一个元素上使用，因为 `v-for` 的优先级更高。应将 `v-if` 移至外层容器或内层元素。
- **【推荐】** 逻辑关注点应组织在独立的 `useXXX.ts` 文件中（自定义 Hooks），然后在组件中导入使用。

## 7. TypeScript 最佳实践

- **【强制】** 开启严格模式 (`"strict": true`)。
- **【强制】** 避免使用 `any` 类型。如果类型确实未知，优先使用 `unknown`，并在使用前进行类型断言或类型守卫。
- **【强制】** 为所有函数参数和返回值提供明确的类型注解。
- **【推荐】** 使用 `interface` 定义对象结构，使用 `type` 定义联合类型、交叉类型或函数签名。
- **【推荐】** API 响应数据、Pinia state 等重要数据结构，应在 `src/types/` 目录下创建专门的 `.d.ts` 文件进行定义。

## 8. 组件开发规范

- **单一职责原则**: 每个组件应只做一件事，并把它做好。
- **原子化设计**: 将复杂组件拆分为更小的、可复用的"原子"组件。
- **Props down, Events up**: 数据通过 `props` 从父组件流向子组件，子组件通过 `emits` 将事件通知父组件。严禁子组件直接修改父组件的 `props`。
- **插槽 (Slots)**: 对于需要灵活定制部分内容的组件，应使用插槽。
- **Provide/Inject**: 仅在开发组件库或需要深层跨组件通信时谨慎使用。

## 9. 样式方案 (CSS/SCSS)

- **【强制】** 组件中的 `<style>` 标签必须添加 `scoped` 属性，以避免样式全局污染。
- **【推荐】** 使用 SCSS/Sass 作为 CSS 预处理器，利用其变量、混入 (mixin)、嵌套等特性。
- **【推荐】** 全局样式、主题变量等应放在 `src/assets/styles/` 目录下进行统一管理。
- **深度选择器**: 如需修改子组件的深层样式，应使用 `::v-deep` 或 `:deep()` 选择器。

## 10. 状态管理规范 (Pinia)

- **【强制】** 所有跨组件、跨页面的共享状态必须通过 Pinia 进行管理。
- **模块化**: 每个功能模块应有自己独立的 Store，定义在 `src/store/modules/` 目录下。
- **【强制】** `state` 必须通过函数返回，以避免在服务端渲染 (SSR) 中交叉请求导致的状态污染。
- **【强制】** 修改 `state` 只能通过 `actions` 或 `$patch`。禁止在组件中直接修改 Store 的状态。
- **Actions**: `actions` 中可以包含异步逻辑。
- **Getters**: 用于派生或计算状态，类似组件的 `computed`。

## 11. 路由管理规范 (Vue Router)

- **【强制】** 使用 `history` 模式以获得更自然的 URL。
- **【强制】** 路由配置应按模块拆分，在 `src/router/` 目录下统一管理。
- **【推荐】** 对页面组件（`views`）使用路由懒加载（动态 `import()`），以优化首屏加载速度。
- **【强制】** 必须设置路由导航守卫 (`beforeEach`) 来处理页面访问权限和用户登录状态的校验。

## 12. API 请求规范

- **【强制】** 必须对 HTTP 客户端 (Axios) 进行统一封装，存放于 `src/utils/request.ts`。
- **封装内容**:
    - **统一配置**: `baseURL`, `timeout` 等。
    - **请求拦截器**: 统一添加认证信息 (如 `token`) 到请求头。
    - **响应拦截器**: 统一处理响应数据结构（剥离无用层级）、处理后端返回的业务错误码、处理 `token` 失效等情况。
- **【强制】** 所有 API 请求函数应按功能模块组织在 `src/api/` 目录下。

## 13. 文档与注释规范

- **【强制】** 遵循 JSDoc 规范。
- **【强制】** 所有公共函数、自定义 Hooks、复杂逻辑块都必须有清晰的 JSDoc 注释，说明其功能、参数和返回值。
- **【强制】** 所有 Vue 组件的 `<script>` 部分应在顶部注释，简要说明组件的功能和用途。

## 14. 配置与环境管理

- **【强制】** 使用 `.env` 文件系列来管理环境变量。
    - `.env`: 所有环境下都加载的变量。
    - `.env.development`: 仅在开发环境下加载。
    - `.env.production`: 仅在生产环境下加载。
- **【强制】** 环境变量必须以 `VITE_` 开头才能在客户端代码中通过 `import.meta.env` 访问。
- **【红线】** 严禁将任何敏感信息（如密钥）直接硬编码在代码或 `.env` 文件中，并提交到版本库。

## 15. 错误处理与日志记录

- **前端错误**: 使用 Vue 的全局错误处理器 (`app.config.errorHandler`) 捕获组件渲染和生命周期中的错误，并上报到日志系统。
- **异步错误**: 使用 `try...catch` 捕获 Promise 中的异步错误。在统一的请求封装中处理 API 异常。
- **日志**:
    - 开发环境可使用 `console.log`, `console.error` 等。
    - 生产环境应移除或替换为专业的第三方日志服务（如 Sentry, LogRocket），以便于追踪和分析线上问题。

## 16. 性能优化指导

- **路由懒加载**。
- **组件动态导入**: 使用 `defineAsyncComponent`。
- **图片资源优化**: 使用 WebP 格式、图片懒加载、CDN。
- **代码分割**: Vite 默认支持。合理使用 `manualChunks` 进行手动分割。
- **长列表优化**: 使用虚拟滚动库（如 `vue-virtual-scroller`）。
- **善用 `v-memo` 和 `computed`** 缓存计算结果。
- **使用 `bundle-analyzer`** 分析打包产物，移除不必要的依赖。

## 17. 安全编码规范

- **XSS 防护**: 永远不要使用 `v-html` 渲染来自用户的未知内容。Vue 默认的模板插值 (`{{ }}`) 是安全的。
- **CSRF 防护**: 确保后端 API 有 CSRF 防护机制（如 Token 或 SameSite cookies）。
- **依赖安全**: 定期运行 `npm audit` 或使用 Snyk 等工具扫描依赖库中的已知漏洞。
- **URL 跳转**: 对所有重定向 URL 进行白名单验证，防止开放重定向漏洞。

## 18. 单元与组件测试规范

- **【推荐】技术选型**:
    - **测试运行器**: **Vitest** (与 Vite 无缝集成)。
    - **测试库**: **Vue Test Utils**。
    - **断言库**: Chai (Vitest 内置)。
- **测试覆盖率**: 核心业务逻辑和公共组件的测试覆盖率应不低于 80%。
- **测试什么**:
    - 公共工具函数。
    - 自定义 Hooks。
    - 组件的 props 渲染、事件触发、插槽内容。
    - Pinia Store 的 state 变更和 getters 计算。
- **Mocking**: 使用 Vitest 的 `vi.mock` 来模拟 API 请求、第三方库等外部依赖。

## 19. Git 工作流与代码审查

- **【强制】** 严格遵循 **[编码规范.md](./编码规范.md)** 中定义的 **Git 工作流** 和 **Conventional Commits** 提交消息规范。
- **【强制】** 严格遵循 **[代码审查指南.md](./代码审查指南.md)** 中定义的审查流程和要点。

## 20. CI/CD 集成规范

- **【推荐】** 使用 **GitHub Actions** 或类似的 CI/CD 工具。
- **流水线步骤**:
    1.  安装依赖 (`npm install`)。
    2.  运行代码质量检查 (`npm run lint`)。
    3.  运行单元测试 (`npm run test`)。
    4.  执行生产构建 (`npm run build`)。
    5.  (可选) 将构建产物部署到服务器或 CDN。

## 21. 附录：配置文件示例

### 21.1 `.eslintrc.cjs`
```javascript
module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:vue/vue3-recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    ecmaVersion: 'latest',
    parser: '@typescript-eslint/parser',
    sourceType: 'module',
  },
  plugins: ['vue', '@typescript-eslint'],
  rules: {
    'prettier/prettier': 'error',
    'vue/multi-word-component-names': 'off', // or 'warn'
  },
};
```

### 21.2 `.prettierrc`
```json
{
  "semi": false,
  "singleQuote": true,
  "printWidth": 100,
  "trailingComma": "es5",
  "arrowParens": "always"
}
```

### 21.3 `vite.config.ts`
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://your-backend-api.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
})
```