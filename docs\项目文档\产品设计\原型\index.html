<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茂名市地质灾害预警平台 - 高保真原型展示</title>
    <link rel="stylesheet" href="common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 展示页面样式 */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: var(--spacing-lg);
        }

        .prototype-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: var(--radius-xl);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .prototype-header {
            background: linear-gradient(135deg, #1E40AF 0%, #3B82F6 100%);
            color: white;
            padding: var(--spacing-2xl);
            text-align: center;
        }

        .prototype-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
        }

        .prototype-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: var(--spacing-lg);
        }

        .prototype-content {
            padding: var(--spacing-2xl);
        }

        .section {
            margin-bottom: var(--spacing-3xl);
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #1E40AF;
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .section-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #1E40AF, #3B82F6);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .prototype-card {
            background-color: #F9FAFB;
            border: 1px solid #E5E7EB;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .prototype-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            border-color: #3B82F6;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
        }

        .card-description {
            color: #6B7280;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: var(--spacing-md);
        }

        .card-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .card-features li {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            color: #374151;
            font-size: 13px;
            margin-bottom: var(--spacing-xs);
        }

        .card-features li:last-child {
            margin-bottom: 0;
        }

        .feature-icon {
            color: #10B981;
            font-size: 12px;
        }

        .admin-card {
            background: linear-gradient(135deg, #0F172A 0%, #1E293B 100%);
            color: white;
        }

        .admin-card .card-title {
            color: white;
        }

        .admin-card .card-description {
            color: #CBD5E1;
        }

        .admin-card .card-features li {
            color: #E2E8F0;
        }

        .mobile-preview {
            display: flex;
            justify-content: center;
            gap: var(--spacing-xl);
            margin-top: var(--spacing-xl);
        }

        .phone-mockup {
            width: 280px;
            height: 560px;
            background-color: #1F2937;
            border-radius: 24px;
            padding: 20px;
            position: relative;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background-color: white;
            border-radius: 16px;
            overflow: hidden;
            position: relative;
        }

        .phone-content {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: var(--spacing-lg);
        }

        .docs-section {
            background-color: #F9FAFB;
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            margin-top: var(--spacing-xl);
        }

        .docs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        .doc-item {
            background-color: white;
            border: 1px solid #E5E7EB;
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            text-decoration: none;
            color: inherit;
            transition: all 0.2s ease;
        }

        .doc-item:hover {
            border-color: #3B82F6;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .doc-title {
            font-weight: 600;
            color: #111827;
            margin-bottom: var(--spacing-xs);
        }

        .doc-desc {
            font-size: 13px;
            color: #6B7280;
        }

        .footer {
            background-color: #1F2937;
            color: white;
            padding: var(--spacing-xl);
            text-align: center;
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .footer-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }

        .footer-text {
            color: #D1D5DB;
            line-height: 1.6;
            margin-bottom: var(--spacing-lg);
        }

        .footer-meta {
            font-size: 12px;
            color: #9CA3AF;
            border-top: 1px solid #374151;
            padding-top: var(--spacing-md);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .mobile-preview {
                flex-direction: column;
                align-items: center;
            }

            .phone-mockup {
                width: 240px;
                height: 480px;
            }
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <!-- 原型头部 -->
        <div class="prototype-header">
            <h1 class="prototype-title">茂名市地质灾害预警平台</h1>
            <p class="prototype-subtitle">高保真UI原型展示 - 守护茂名，预防地质灾害</p>
        </div>

        <!-- 原型内容 -->
        <div class="prototype-content">
            <!-- 公众查询服务 -->
            <div class="section">
                <h2 class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    公众查询服务
                </h2>

                <div class="prototype-grid">
                    <div class="prototype-card" onclick="window.open('Web/public-query.html', '_blank')">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #1E40AF, #3B82F6);">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <div class="card-title">Web端查询界面</div>
                        </div>
                        <div class="card-description">
                            为公众提供地质灾害风险查询服务和预警信息查看功能，基于天地图展示灾害点和防范区，支持预警信息列表查看和地图交互。
                        </div>
                        <ul class="card-features">
                            <li><i class="fas fa-check feature-icon"></i> 天地图集成展示</li>
                            <li><i class="fas fa-check feature-icon"></i> 预警信息列表</li>
                            <li><i class="fas fa-check feature-icon"></i> 区域筛选功能</li>
                            <li><i class="fas fa-check feature-icon"></i> 预警详情对话框</li>
                        </ul>
                    </div>

                    <div class="prototype-card" onclick="window.open('Mobile/mobile-query.html', '_blank')">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #10B981, #059669);">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="card-title">移动端查询界面</div>
                        </div>
                        <div class="card-description">
                            移动端优化的查询体验，采用浮动按钮设计，支持GPS定位和触屏操作，抽屉式预警信息列表提供完整功能。
                        </div>
                        <ul class="card-features">
                            <li><i class="fas fa-check feature-icon"></i> 浮动按钮设计</li>
                            <li><i class="fas fa-check feature-icon"></i> 抽屉式预警列表</li>
                            <li><i class="fas fa-check feature-icon"></i> 上拉加载更多</li>
                            <li><i class="fas fa-check feature-icon"></i> 地图联动定位</li>
                        </ul>
                    </div>

                    <div class="prototype-card" onclick="window.open('Mobile/wechat-official.html', '_blank')">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #07C160, #06AD56);">
                                <i class="fab fa-weixin"></i>
                            </div>
                            <div class="card-title">微信公众号界面</div>
                        </div>
                        <div class="card-description">
                            模拟茂名市自然资源局微信公众号，提供便民服务入口，支持跳转到查询功能。
                        </div>
                        <ul class="card-features">
                            <li><i class="fas fa-check feature-icon"></i> 微信界面模拟</li>
                            <li><i class="fas fa-check feature-icon"></i> 功能菜单设计</li>
                            <li><i class="fas fa-check feature-icon"></i> 跳转流程演示</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 管理后台系统 -->
            <div class="section">
                <h2 class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    管理后台系统
                </h2>

                <div class="prototype-grid">
                    <div class="prototype-card admin-card" onclick="window.open('Web/admin-login.html', '_blank')">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #3B82F6, #1E40AF);">
                                <i class="fas fa-sign-in-alt"></i>
                            </div>
                            <div class="card-title">管理员登录</div>
                        </div>
                        <div class="card-description">
                            安全的管理员登录界面，支持用户名密码验证、验证码校验和记住登录功能。
                        </div>
                        <ul class="card-features">
                            <li><i class="fas fa-check feature-icon"></i> 安全登录验证</li>
                            <li><i class="fas fa-check feature-icon"></i> 验证码保护</li>
                            <li><i class="fas fa-check feature-icon"></i> 记住登录状态</li>
                            <li><i class="fas fa-check feature-icon"></i> 暗黑主题设计</li>
                        </ul>
                    </div>

                    <div class="prototype-card admin-card" onclick="window.open('Web/admin-dashboard.html', '_blank')">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #8B5CF6, #7C3AED);">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="card-title">系统仪表板</div>
                        </div>
                        <div class="card-description">
                            管理后台主界面，提供系统概览、数据统计、快捷操作和系统状态监控功能。
                        </div>
                        <ul class="card-features">
                            <li><i class="fas fa-check feature-icon"></i> 数据统计展示</li>
                            <li><i class="fas fa-check feature-icon"></i> 系统状态监控</li>
                            <li><i class="fas fa-check feature-icon"></i> 最近活动记录</li>
                            <li><i class="fas fa-check feature-icon"></i> 响应式侧边栏</li>
                        </ul>
                    </div>

                    <div class="prototype-card admin-card" onclick="window.open('Web/admin-data-management.html', '_blank')">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #F59E0B, #D97706);">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="card-title">数据管理</div>
                        </div>
                        <div class="card-description">
                            地质灾害点数据管理界面，支持搜索筛选、批量操作、数据导入导出等功能。
                        </div>
                        <ul class="card-features">
                            <li><i class="fas fa-check feature-icon"></i> 数据表格展示</li>
                            <li><i class="fas fa-check feature-icon"></i> 多条件筛选</li>
                            <li><i class="fas fa-check feature-icon"></i> 批量操作功能</li>
                            <li><i class="fas fa-check feature-icon"></i> 导入导出支持</li>
                        </ul>
                    </div>

                    <div class="prototype-card admin-card" onclick="window.open('Web/admin-user-management.html', '_blank')">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #10B981, #059669);">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-title">用户管理</div>
                        </div>
                        <div class="card-description">
                            系统用户管理界面，支持用户增删改查、角色权限管理、状态控制等完整的用户管理功能。
                        </div>
                        <ul class="card-features">
                            <li><i class="fas fa-check feature-icon"></i> 完整CRUD操作</li>
                            <li><i class="fas fa-check feature-icon"></i> 侧边抽屉交互</li>
                            <li><i class="fas fa-check feature-icon"></i> Toast通知系统</li>
                            <li><i class="fas fa-check feature-icon"></i> 表单验证功能</li>
                        </ul>
                    </div>

                    <div class="prototype-card admin-card" onclick="window.open('Web/admin-warning.html', '_blank')">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #EF4444, #DC2626);">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-title">预警发布</div>
                        </div>
                        <div class="card-description">
                            地质灾害预警信息发布界面，支持多等级预警、多渠道发布和实时预览功能。
                        </div>
                        <ul class="card-features">
                            <li><i class="fas fa-check feature-icon"></i> 四级预警等级</li>
                            <li><i class="fas fa-check feature-icon"></i> 多渠道发布</li>
                            <li><i class="fas fa-check feature-icon"></i> 实时预览功能</li>
                            <li><i class="fas fa-check feature-icon"></i> 草稿保存功能</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 设计文档 -->
            <div class="docs-section">
                <h2 class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    设计文档
                </h2>

                <div class="docs-grid">
                    <a href="docs/需求理解与确认纪要.md" class="doc-item">
                        <div class="doc-title">需求理解与确认纪要</div>
                        <div class="doc-desc">详细的需求分析和用户画像</div>
                    </a>
                    <a href="docs/设计风格提案.md" class="doc-item">
                        <div class="doc-title">设计风格提案</div>
                        <div class="doc-desc">4种设计风格方向和推荐方案</div>
                    </a>
                    <a href="docs/界面清单与交互流程图.md" class="doc-item">
                        <div class="doc-title">界面清单与交互流程图</div>
                        <div class="doc-desc">24个核心界面和用户流程设计</div>
                    </a>
                    <a href="docs/视觉风格规范与关键界面概念稿.md" class="doc-item">
                        <div class="doc-title">视觉风格规范</div>
                        <div class="doc-desc">完整的色彩、字体、组件规范</div>
                    </a>
                    <a href="docs/设计优化建议与修订方案.md" class="doc-item">
                        <div class="doc-title">设计优化建议</div>
                        <div class="doc-desc">批判性反思和优化方案</div>
                    </a>
                    <a href="docs/设计方案综合审视结论.md" class="doc-item">
                        <div class="doc-title">设计方案综合审视结论</div>
                        <div class="doc-desc">方案成熟度评估和结论</div>
                    </a>
                    <a href="docs/高保真原型实现总结.md" class="doc-item">
                        <div class="doc-title">高保真原型实现总结</div>
                        <div class="doc-desc">原型实现成果和质量评估</div>
                    </a>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <div class="footer-content">
                <h3 class="footer-title">茂名市地质灾害预警平台</h3>
                <p class="footer-text">
                    本原型展示了茂名市地质灾害预警平台公众查询服务和管理后台系统的完整UI设计方案。
                    采用现代政务简约风格融合科技安全感知元素，确保专业性与易用性的完美平衡。
                </p>
                <div class="footer-meta">
                    <p>设计人：梁铭显 | 创建日期：2025-07-11</p>
                    <p>茂名市自然资源勘探测绘院</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.prototype-card');

            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
