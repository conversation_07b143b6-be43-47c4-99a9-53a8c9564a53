/* 茂名市地质灾害预警平台 - 通用样式 */
/* 创建日期: 2025-07-11 */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 字体定义 */
:root {
    /* 字体家族 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;

    /* 公众界面色彩（明亮主题） */
    --public-primary: #1E40AF;
    --public-secondary: #3B82F6;
    --public-light: #DBEAFE;
    --public-success: #10B981;
    --public-warning: #F59E0B;
    --public-danger: #EF4444;
    --public-info: #3B82F6;
    --public-bg-primary: #FFFFFF;
    --public-bg-secondary: #F9FAFB;
    --public-border: #E5E7EB;
    --public-text-primary: #111827;
    --public-text-secondary: #6B7280;
    --public-text-tertiary: #9CA3AF;

    /* 管理后台色彩（暗黑主题） */
    --admin-primary: #3B82F6;
    --admin-secondary: #1E40AF;
    --admin-accent: #06B6D4;
    --admin-success: #10B981;
    --admin-warning: #F59E0B;
    --admin-danger: #EF4444;
    --admin-info: #3B82F6;
    --admin-bg-primary: #0F172A;
    --admin-bg-secondary: #1E293B;
    --admin-bg-tertiary: #334155;
    --admin-border: #475569;
    --admin-text-primary: #F1F5F9;
    --admin-text-secondary: #CBD5E1;
    --admin-text-tertiary: #94A3B8;

    /* 间距系统 */
    --spacing-xs: 8px;
    --spacing-sm: 12px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;

    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;

    /* 阴影 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* 基础样式 */
body {
    font-family: var(--font-family);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 公众界面基础样式 */
.public-theme {
    background-color: var(--public-bg-primary);
    color: var(--public-text-primary);
}

/* 管理后台基础样式 */
.admin-theme {
    background-color: var(--admin-bg-primary);
    color: var(--admin-text-primary);
}

/* 字体层级 */
.text-h1 { font-size: 32px; line-height: 40px; font-weight: 700; }
.text-h2 { font-size: 24px; line-height: 32px; font-weight: 600; }
.text-h3 { font-size: 20px; line-height: 28px; font-weight: 600; }
.text-h4 { font-size: 18px; line-height: 24px; font-weight: 500; }
.text-body-lg { font-size: 16px; line-height: 24px; font-weight: 400; }
.text-body { font-size: 14px; line-height: 20px; font-weight: 400; }
.text-body-sm { font-size: 12px; line-height: 16px; font-weight: 400; }
.text-button { font-size: 14px; line-height: 20px; font-weight: 500; }
.text-caption { font-size: 12px; line-height: 16px; font-weight: 400; }
.text-label { font-size: 14px; line-height: 20px; font-weight: 500; }

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    text-decoration: none;
    transition: all 0.2s ease;
    min-height: 40px;
}

/* 正方形按钮组件 - 图标在上，文字在下 */
.btn-square {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    padding: var(--spacing-sm);
    border-radius: var(--radius-lg);
    border: none;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.2;
    text-decoration: none;
    transition: all 0.2s ease;
    gap: 6px;
}

.btn-square i {
    font-size: 20px;
    margin-bottom: 2px;
}

.btn-square span {
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* 中等尺寸正方形按钮 */
.btn-square-md {
    width: 100px;
    height: 100px;
    font-size: 13px;
    gap: 8px;
}

.btn-square-md i {
    font-size: 24px;
}

/* 大尺寸正方形按钮 */
.btn-square-lg {
    width: 120px;
    height: 120px;
    font-size: 14px;
    gap: 10px;
}

.btn-square-lg i {
    font-size: 28px;
}

.btn-primary-public {
    background-color: var(--public-primary);
    color: white;
}

.btn-primary-public:hover {
    background-color: #1E3A8A;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary-admin {
    background-color: var(--admin-primary);
    color: white;
}

.btn-primary-admin:hover {
    background-color: #2563EB;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary-public {
    background-color: transparent;
    color: var(--public-primary);
    border: 1px solid var(--public-border);
}

.btn-secondary-public:hover {
    background-color: var(--public-light);
    border-color: var(--public-primary);
}

.btn-secondary-admin {
    background-color: transparent;
    color: var(--admin-primary);
    border: 1px solid var(--admin-border);
}

.btn-secondary-admin:hover {
    background-color: var(--admin-bg-tertiary);
    border-color: var(--admin-primary);
}

/* 正方形按钮主题样式 */
.btn-square-primary-public {
    background-color: var(--public-primary);
    color: white;
}

.btn-square-primary-public:hover {
    background-color: #1E3A8A;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-square-primary-admin {
    background-color: var(--admin-primary);
    color: white;
}

.btn-square-primary-admin:hover {
    background-color: #2563EB;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-square-secondary-public {
    background-color: transparent;
    color: var(--public-primary);
    border: 1px solid var(--public-border);
}

.btn-square-secondary-public:hover {
    background-color: var(--public-light);
    border-color: var(--public-primary);
    transform: translateY(-2px);
}

.btn-square-secondary-admin {
    background-color: transparent;
    color: var(--admin-primary);
    border: 1px solid var(--admin-border);
}

.btn-square-secondary-admin:hover {
    background-color: var(--admin-bg-tertiary);
    border-color: var(--admin-primary);
    transform: translateY(-2px);
}

/* 输入框组件 */
.input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    border: 1px solid;
    font-size: 14px;
    line-height: 20px;
    transition: all 0.2s ease;
    min-height: 40px;
}

.input-public {
    border-color: var(--public-border);
    background-color: var(--public-bg-primary);
    color: var(--public-text-primary);
}

.input-public:focus {
    outline: none;
    border-color: var(--public-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-admin {
    border-color: var(--admin-border);
    background-color: var(--admin-bg-tertiary);
    color: var(--admin-text-primary);
}

.input-admin:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 卡片组件 */
.card {
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: all 0.2s ease;
}

.card-public {
    background-color: var(--public-bg-primary);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--public-border);
}

.card-public:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-admin {
    background-color: var(--admin-bg-tertiary);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--admin-border);
}

.card-admin:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* 导航栏组件 */
.navbar {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
}

.navbar-public {
    background-color: var(--public-bg-primary);
    border-bottom: 1px solid var(--public-border);
    box-shadow: var(--shadow-sm);
}

.navbar-admin {
    background-color: var(--admin-bg-secondary);
    border-bottom: 1px solid var(--admin-border);
}

/* 侧边栏 */
.sidebar {
    width: 240px;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.sidebar-admin {
    background-color: var(--admin-bg-secondary);
    border-right: 1px solid var(--admin-border);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 64px;
    }

    .btn {
        min-height: 44px;
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .input {
        min-height: 44px;
        padding: var(--spacing-md);
    }
}

/* 表格组件 */
.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.table-public {
    background-color: var(--public-bg-primary);
}

.table-admin {
    background-color: var(--admin-bg-tertiary);
}

.table th {
    padding: var(--spacing-md);
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid;
}

.table-public th {
    background-color: var(--public-bg-secondary);
    color: var(--public-text-primary);
    border-color: var(--public-border);
}

.table-admin th {
    background-color: var(--admin-bg-secondary);
    color: var(--admin-text-primary);
    border-color: var(--admin-border);
}

.table td {
    padding: var(--spacing-md);
    border-bottom: 1px solid;
}

.table-public td {
    border-color: var(--public-border);
    color: var(--public-text-primary);
}

.table-admin td {
    border-color: var(--admin-border);
    color: var(--admin-text-primary);
}

.table tbody tr:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

/* 徽章组件 */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    color: white;
}

.badge-danger { background-color: var(--public-danger); }
.badge-warning { background-color: var(--public-warning); }
.badge-success { background-color: var(--public-success); }
.badge-info { background-color: var(--public-info); }

/* 工具类 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.ml-2 { margin-left: 8px; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--public-primary); }
.text-secondary { color: var(--public-text-secondary); }
.text-success { color: var(--public-success); }
.text-warning { color: var(--public-warning); }
.text-danger { color: var(--public-danger); }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

@media (max-width: 768px) {
    .md\:inline { display: inline; }
}
