<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警发布 - 茂名市地质灾害预警平台</title>
    <link rel="stylesheet" href="../common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 预警发布页面样式 */
        .warning-layout {
            display: flex;
            min-height: 100vh;
        }

        .warning-content {
            flex: 1;
            margin-left: 240px;
            background-color: var(--admin-bg-primary);
        }

        .warning-main {
            padding: var(--spacing-lg);
        }

        .form-container {
            background-color: var(--admin-bg-secondary);
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-lg);
        }

        .form-section {
            margin-bottom: var(--spacing-xl);
        }

        .section-title {
            color: var(--admin-text-primary);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .form-label {
            color: var(--admin-text-primary);
            font-size: 14px;
            font-weight: 500;
        }

        .form-label.required::after {
            content: ' *';
            color: var(--admin-danger);
        }

        .warning-level-selector {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-md);
        }

        .level-option {
            padding: var(--spacing-md);
            border: 2px solid var(--admin-border);
            border-radius: var(--radius-lg);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: var(--admin-bg-tertiary);
        }

        .level-option:hover {
            border-color: var(--admin-primary);
        }

        .level-option.selected {
            border-color: var(--admin-primary);
            background-color: rgba(59, 130, 246, 0.1);
        }

        .level-icon {
            font-size: 24px;
            margin-bottom: var(--spacing-sm);
        }

        .level-red { color: #DC2626; }
        .level-orange { color: #EA580C; }
        .level-yellow { color: #CA8A04; }
        .level-blue { color: #2563EB; }

        .level-name {
            font-weight: 600;
            color: var(--admin-text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .level-desc {
            font-size: 12px;
            color: var(--admin-text-secondary);
        }

        .textarea {
            width: 100%;
            min-height: 120px;
            padding: var(--spacing-md);
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-lg);
            background-color: var(--admin-bg-tertiary);
            color: var(--admin-text-primary);
            font-size: 14px;
            resize: vertical;
            font-family: inherit;
        }

        .textarea:focus {
            outline: none;
            border-color: var(--admin-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .preview-section {
            background-color: var(--admin-bg-secondary);
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-lg);
        }

        .preview-card {
            background-color: var(--admin-bg-tertiary);
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
        }

        .preview-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }

        .preview-level {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-md);
            font-size: 12px;
            font-weight: 600;
            color: white;
        }

        .preview-time {
            color: var(--admin-text-secondary);
            font-size: 12px;
        }

        .preview-content {
            color: var(--admin-text-primary);
            line-height: 1.6;
        }

        .action-bar {
            background-color: var(--admin-bg-secondary);
            border: 1px solid var(--admin-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-left {
            display: flex;
            gap: var(--spacing-md);
        }

        .action-right {
            display: flex;
            gap: var(--spacing-md);
        }

        .publish-btn {
            background: linear-gradient(135deg, var(--admin-warning) 0%, #EA580C 100%);
            color: white;
            border: none;
            padding: var(--spacing-md) var(--spacing-xl);
            border-radius: var(--radius-lg);
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .publish-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 15px -3px rgba(234, 88, 12, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .warning-content {
                margin-left: 64px;
            }

            .warning-level-selector {
                grid-template-columns: repeat(2, 1fr);
            }

            .action-bar {
                flex-direction: column;
                gap: var(--spacing-md);
            }
        }
    </style>
</head>
<body class="admin-theme">
    <div class="warning-layout">
        <!-- 侧边栏（复用） -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="sidebar-title">管理后台</div>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-group">
                    <div class="nav-group-title">主要功能</div>
                    <a href="admin-dashboard.html" class="nav-item">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span>仪表板</span>
                    </a>
                    <a href="admin-data-management.html" class="nav-item">
                        <i class="fas fa-map-marked-alt nav-icon"></i>
                        <span>地质灾害点</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-shield-alt nav-icon"></i>
                        <span>风险防范区</span>
                    </a>
                    <a href="#" class="nav-item active">
                        <i class="fas fa-exclamation-triangle nav-icon"></i>
                        <span>预警发布</span>
                    </a>
                </div>

                <div class="nav-group">
                    <div class="nav-group-title">系统管理</div>
                    <a href="#" class="nav-item">
                        <i class="fas fa-users nav-icon"></i>
                        <span>用户管理</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-key nav-icon"></i>
                        <span>权限管理</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-history nav-icon"></i>
                        <span>操作日志</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i class="fas fa-cog nav-icon"></i>
                        <span>系统配置</span>
                    </a>
                </div>
            </nav>
        </div>

        <!-- 主要内容区域 -->
        <div class="warning-content">
            <!-- 顶部工具栏 -->
            <div class="top-bar">
                <div class="breadcrumb">
                    <i class="fas fa-home"></i>
                    <span class="breadcrumb-separator">/</span>
                    <span>预警发布</span>
                    <span class="breadcrumb-separator">/</span>
                    <span>新建预警</span>
                </div>

                <div class="top-bar-actions">
                    <button class="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>

                    <div class="user-menu">
                        <div class="user-avatar">管</div>
                        <div>
                            <div style="color: var(--admin-text-primary); font-size: 14px; font-weight: 500;">管理员</div>
                            <div style="color: var(--admin-text-tertiary); font-size: 12px;">在线</div>
                        </div>
                        <i class="fas fa-chevron-down" style="color: var(--admin-text-tertiary);"></i>
                    </div>
                </div>
            </div>

            <!-- 页面头部 -->
            <div class="data-header">
                <h1 class="text-h2" style="color: var(--admin-text-primary);">发布地质灾害预警</h1>
                <p style="color: var(--admin-text-secondary); margin-top: var(--spacing-sm);">
                    创建和发布地质灾害预警信息，及时通知相关区域的群众和部门
                </p>
            </div>

            <!-- 主要内容 -->
            <div class="warning-main">
                <!-- 预警信息表单 -->
                <div class="form-container">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-info-circle"></i>基本信息
                        </h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">预警标题</label>
                                <input type="text" class="input input-admin" placeholder="请输入预警标题" id="warningTitle">
                            </div>
                            <div class="form-group">
                                <label class="form-label required">影响区域</label>
                                <select class="input input-admin" id="warningArea">
                                    <option value="">请选择影响区域</option>
                                    <option value="maonan">茂南区</option>
                                    <option value="dianbai">电白区</option>
                                    <option value="gaozhou">高州市</option>
                                    <option value="huazhou">化州市</option>
                                    <option value="xinyi">信宜市</option>
                                    <option value="all">全市</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">生效时间</label>
                                <input type="datetime-local" class="input input-admin" id="startTime">
                            </div>
                            <div class="form-group">
                                <label class="form-label required">失效时间</label>
                                <input type="datetime-local" class="input input-admin" id="endTime">
                            </div>
                        </div>
                    </div>

                    <!-- 预警等级 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-exclamation-triangle"></i>预警等级
                        </h3>

                        <div class="warning-level-selector">
                            <div class="level-option" data-level="red">
                                <div class="level-icon level-red">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="level-name">红色预警</div>
                                <div class="level-desc">特别严重</div>
                            </div>
                            <div class="level-option" data-level="orange">
                                <div class="level-icon level-orange">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="level-name">橙色预警</div>
                                <div class="level-desc">严重</div>
                            </div>
                            <div class="level-option" data-level="yellow">
                                <div class="level-icon level-yellow">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="level-name">黄色预警</div>
                                <div class="level-desc">较重</div>
                            </div>
                            <div class="level-option" data-level="blue">
                                <div class="level-icon level-blue">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="level-name">蓝色预警</div>
                                <div class="level-desc">一般</div>
                            </div>
                        </div>
                    </div>

                    <!-- 预警内容 -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-edit"></i>预警内容
                        </h3>

                        <div class="form-group">
                            <label class="form-label required">预警内容</label>
                            <textarea class="textarea" placeholder="请输入详细的预警内容..." id="warningContent"></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">发布渠道</label>
                                <div style="display: flex; flex-wrap: wrap; gap: var(--spacing-md); margin-top: var(--spacing-sm);">
                                    <label style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                        <input type="checkbox" checked style="accent-color: var(--admin-primary);">
                                        <span style="color: var(--admin-text-primary);">网站公告</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                        <input type="checkbox" checked style="accent-color: var(--admin-primary);">
                                        <span style="color: var(--admin-text-primary);">微信公众号</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                        <input type="checkbox" style="accent-color: var(--admin-primary);">
                                        <span style="color: var(--admin-text-primary);">短信通知</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                        <input type="checkbox" style="accent-color: var(--admin-primary);">
                                        <span style="color: var(--admin-text-primary);">应急广播</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 预警预览 -->
                <div class="preview-section">
                    <h3 class="section-title">
                        <i class="fas fa-eye"></i>预警预览
                    </h3>

                    <div class="preview-card" id="previewCard">
                        <div class="preview-header">
                            <span class="preview-level" id="previewLevel" style="background-color: var(--admin-text-tertiary);">请选择预警等级</span>
                            <span class="preview-time" id="previewTime">2025-07-11 14:30</span>
                        </div>
                        <h4 style="color: var(--admin-text-primary); margin-bottom: var(--spacing-sm);" id="previewTitle">请输入预警标题</h4>
                        <div class="preview-content" id="previewContent">请输入预警内容...</div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-bar">
                    <div class="action-left">
                        <button class="btn btn-secondary-admin">
                            <i class="fas fa-save mr-2"></i>保存草稿
                        </button>
                        <button class="btn btn-secondary-admin">
                            <i class="fas fa-eye mr-2"></i>预览效果
                        </button>
                    </div>
                    <div class="action-right">
                        <button class="btn btn-secondary-admin">
                            <i class="fas fa-times mr-2"></i>取消
                        </button>
                        <button class="publish-btn" id="publishBtn">
                            <i class="fas fa-broadcast-tower mr-2"></i>立即发布
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 预警等级选择
        document.querySelectorAll('.level-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.level-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');

                const level = this.dataset.level;
                const levelColors = {
                    red: '#DC2626',
                    orange: '#EA580C',
                    yellow: '#CA8A04',
                    blue: '#2563EB'
                };

                const levelNames = {
                    red: '红色预警',
                    orange: '橙色预警',
                    yellow: '黄色预警',
                    blue: '蓝色预警'
                };

                const previewLevel = document.getElementById('previewLevel');
                previewLevel.textContent = levelNames[level];
                previewLevel.style.backgroundColor = levelColors[level];
            });
        });

        // 实时预览更新
        document.getElementById('warningTitle').addEventListener('input', function() {
            document.getElementById('previewTitle').textContent = this.value || '请输入预警标题';
        });

        document.getElementById('warningContent').addEventListener('input', function() {
            document.getElementById('previewContent').textContent = this.value || '请输入预警内容...';
        });

        // 发布按钮
        document.getElementById('publishBtn').addEventListener('click', function() {
            const title = document.getElementById('warningTitle').value;
            const area = document.getElementById('warningArea').value;
            const content = document.getElementById('warningContent').value;
            const selectedLevel = document.querySelector('.level-option.selected');

            if (!title || !area || !content || !selectedLevel) {
                alert('请填写完整的预警信息！');
                return;
            }

            if (confirm('确定要发布这条预警信息吗？发布后将立即通知相关区域的群众。')) {
                alert('预警信息发布成功！\n\n系统将通过以下渠道发布：\n- 网站公告\n- 微信公众号\n- 其他选定渠道');
            }
        });

        // 导航菜单交互
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                if (this.href === '#') {
                    e.preventDefault();
                    if (!this.classList.contains('active')) {
                        alert('功能开发中...');
                    }
                }
            });
        });
    </script>
</body>
</html>
