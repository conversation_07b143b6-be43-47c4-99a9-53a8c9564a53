# 界面清单与交互流程图

---

## 标准文档页眉

| 属性 | 值 |
| :--- | :--- |
| **项目名称** | 茂名市地质灾害预警平台 |
| **文档标题** | 界面清单与交互流程图 |
| **文档版本** | 1.0 |
| **创建日期** | 2025-07-11 |
| **AI助手** | Augment Agent (开发者: Augment Code) |
| **用户代表** | 梁铭显 |
| **依据文档** | 《需求理解与确认纪要》V1.0、《设计风格提案》V1.0 |

---

## 1. 核心界面清单

| 界面ID | 界面名称 | 主要用途/目标 | 核心功能模块/信息点 | 主题 | 备注/状态 |
|:---|:---|:---|:---|:---|:---|
| **公众查询服务模块** |
| P001 | 首页/查询主界面 | 为公众提供地质灾害风险查询入口 | 地图展示、预警信息列表、区域筛选、图例展示、预警详情对话框 | 明亮 | Web端自适应，新增预警信息功能 |
| P002 | 移动端查询界面 | 移动端优化的查询体验 | 地图展示、GPS定位、浮动按钮、抽屉式预警列表、触屏优化 | 明亮 | 微信公众号跳转，预警信息功能完整 |
| **系统管理模块** |
| M001 | 登录界面 | 管理员安全登录系统 | 用户名密码、验证码、记住登录、找回密码 | 暗黑 | 安全验证 |
| M002 | 系统主界面/仪表板 | 系统概览和快速导航 | 数据统计、快捷操作、系统状态、通知消息 | 暗黑 | 管理中心 |
| M003 | 用户管理界面 | 管理系统用户账户 | 用户列表、新增用户、编辑用户、状态管理 | 暗黑 | CRUD操作 |
| M004 | 权限管理界面 | 管理用户角色和权限 | 角色列表、权限分配、访问控制、权限矩阵 | 暗黑 | 权限控制 |
| M005 | 操作日志查询界面 | 查询和审计系统操作记录 | 日志列表、筛选条件、详情查看、导出功能 | 暗黑 | 审计功能 |
| M006 | 系统配置界面 | 管理系统基础参数 | 参数配置、版本管理、系统设置、配置备份 | 暗黑 | 配置管理 |
| **数据管理模块** |
| D001 | 地质灾害点管理界面 | 管理地质灾害点数据 | 灾害点列表、新增编辑、位置管理、状态更新 | 暗黑 | 数据管理 |
| D002 | 风险防范区管理界面 | 管理风险防范区数据 | 防范区列表、新增编辑、区域管理、状态更新 | 暗黑 | 区域管理 |
| D003 | 数据导入界面 | 批量导入地质数据 | 文件上传、格式验证、导入预览、错误处理 | 暗黑 | 批量操作 |
| D004 | 数据导出界面 | 导出地质数据 | 导出条件、格式选择、数据预览、下载管理 | 暗黑 | 数据输出 |
| **预警发布模块** |
| W001 | 预警信息管理界面 | 管理预警信息内容 | 预警列表、新增预警、编辑预警、模板管理 | 暗黑 | 内容管理 |
| W002 | 预警发布界面 | 发布预警信息到各渠道 | 发布渠道、目标区域、发布预览、发布确认 | 暗黑 | 发布操作 |
| W003 | 发布历史查询界面 | 查询预警发布历史记录 | 发布记录、状态跟踪、效果统计、历史查询 | 暗黑 | 历史记录 |
| **通用界面** |
| G001 | 404错误页面 | 页面未找到提示 | 错误提示、返回导航、搜索建议 | 明亮/暗黑 | 错误处理 |
| G002 | 500错误页面 | 服务器错误提示 | 错误提示、联系方式、重试按钮 | 明亮/暗黑 | 错误处理 |
| G003 | 无权限访问页面 | 权限不足提示 | 权限提示、申请权限、返回按钮 | 暗黑 | 权限控制 |
| G004 | 密码重置界面 | 重置用户密码 | 身份验证、新密码设置、安全提示 | 暗黑 | 安全功能 |
| G005 | 个人信息修改界面 | 修改个人账户信息 | 基本信息、密码修改、安全设置 | 暗黑 | 个人设置 |
| G006 | 主入口展示页面 | 原型展示入口 | 界面预览、导航菜单、说明文档 | 明亮 | 原型展示 |

---

## 2. 用户核心流程图

### 2.1 公众查询流程

**目标：** 公众用户查询地质灾害风险信息

```mermaid
flowchart TD
    A[访问网站/微信公众号] --> B{选择查询方式}
    B -->|位置搜索| C[输入地址/选择区域]
    B -->|地图定位| D[GPS定位/手动定位]
    C --> E[系统查询数据库]
    D --> E
    E --> F{是否有风险信息}
    F -->|有风险| G[显示查询结果]
    F -->|无风险| H[显示安全提示]
    G --> I[查看详细信息]
    H --> J[结束查询]
    I --> J
```

**步骤描述：**
1. 用户通过网站或微信公众号进入查询界面
2. 选择位置搜索或地图定位方式
3. 系统根据位置信息查询数据库
4. 展示查询结果或安全提示
5. 用户可查看详细的灾害点或防范区信息

### 2.2 系统管理流程

**目标：** 管理员登录系统进行管理操作

```mermaid
flowchart TD
    A[访问管理后台] --> B[输入登录凭据]
    B --> C{身份验证}
    C -->|验证失败| D[显示错误信息]
    C -->|验证成功| E[进入系统仪表板]
    D --> B
    E --> F{选择管理功能}
    F -->|用户管理| G[用户管理界面]
    F -->|数据管理| H[数据管理界面]
    F -->|预警发布| I[预警发布界面]
    F -->|系统配置| J[系统配置界面]
    G --> K[执行管理操作]
    H --> K
    I --> K
    J --> K
    K --> L[记录操作日志]
    L --> M{继续操作}
    M -->|是| F
    M -->|否| N[安全退出]
```

**步骤描述：**
1. 管理员访问后台登录界面
2. 输入用户名密码进行身份验证
3. 验证成功后进入系统仪表板
4. 选择相应的管理功能模块
5. 执行具体的管理操作
6. 系统记录操作日志
7. 完成操作后安全退出

### 2.3 预警发布流程

**目标：** 管理员发布地质灾害预警信息

```mermaid
flowchart TD
    A[进入预警发布界面] --> B[创建预警信息]
    B --> C[填写预警内容]
    C --> D[选择预警等级]
    D --> E[选择发布区域]
    E --> F[选择发布渠道]
    F --> G[预览预警信息]
    G --> H{确认发布}
    H -->|取消| I[返回编辑]
    H -->|确认| J[执行发布操作]
    I --> C
    J --> K{发布结果}
    K -->|成功| L[记录发布日志]
    K -->|失败| M[显示错误信息]
    L --> N[发布完成]
    M --> O[重试发布]
    O --> J
```

**步骤描述：**
1. 管理员进入预警发布管理界面
2. 创建新的预警信息
3. 填写预警内容和设置预警等级
4. 选择发布的目标区域和渠道
5. 预览预警信息确认无误
6. 执行发布操作并记录日志

---

## 3. 公共界面架构要求

### 3.1 管理后台界面架构标准（所有管理界面必须遵循）

**布局结构要求**：
- 左侧导航栏：240px（展开）/ 60px（收起）
- 主内容区：flex: 1，包含顶部工具栏 + 页面内容区
- 顶部工具栏：64px高度，包含汉堡菜单 + 面包屑 + 用户菜单

**必须使用的公共组件**：
- 侧边栏组件：统一的展开/收起功能
- 汉堡菜单按钮：统一的图标变化逻辑
- 导航菜单项：统一的交互状态样式
- 响应式遮罩层：移动端统一的遮罩控制

**强制遵循的交互规范**：
- 汉堡菜单控制侧边栏展开/收起
- 导航菜单项点击更新active状态和面包屑
- 响应式断点自动调整侧边栏状态
- 移动端遮罩层点击关闭侧边栏

---

## 4. 关键交互模式说明

### 4.1 主导航方式
- **公众界面**：顶部导航栏 + 面包屑导航
- **管理后台**：左侧折叠菜单 + 顶部工具栏 + 汉堡菜单控制

### 4.1.1 管理后台侧边栏交互
- **展开/收起控制**：顶部工具栏左侧汉堡菜单按钮
- **桌面端行为**：侧边栏宽度在240px（展开）和60px（收起）之间切换
- **移动端行为**：侧边栏从左侧滑出/隐藏，显示遮罩层
- **图标智能变化**：
  - 桌面端展开状态：fa-outdent（收起图标）
  - 桌面端收起状态：fa-indent（展开图标）
  - 移动端关闭状态：fa-bars（汉堡图标）
  - 移动端打开状态：fa-times（关闭图标）
- **响应式自动适配**：平板端（768px-1024px）自动收起侧边栏

### 3.2 地图交互
- **缩放控制**：鼠标滚轮/触屏手势
- **图层切换**：图层控制面板
- **信息展示**：点击标记显示弹窗

### 3.3 表单交互
- **实时验证**：输入时即时反馈
- **错误提示**：红色边框 + 错误信息
- **成功反馈**：绿色提示 + 操作确认

### 3.4 数据展示
- **分页加载**：表格数据分页显示
- **筛选排序**：多条件筛选和排序
- **批量操作**：复选框 + 批量操作按钮

### 3.5 移动端优化
- **触屏友好**：按钮尺寸适配触屏操作（最小44px）
- **手势支持**：滑动、缩放、长按等手势
- **响应式布局**：自适应不同屏幕尺寸
- **遮罩层控制**：侧边栏打开时显示半透明遮罩，防止背景滚动
- **滑出动画**：侧边栏从左侧平滑滑出/隐藏

### 3.6 状态管理与反馈
- **导航状态同步**：菜单项active状态与页面内容同步
- **面包屑动态更新**：根据当前页面自动更新导航路径
- **按钮交互反馈**：悬停、按下、激活等状态的视觉反馈
- **加载状态指示**：操作过程中的加载状态展示
- **错误状态处理**：友好的错误信息展示和重试机制

---

## 标准用户审核模块

- **用户意见：** [留空待用户填写]
- **确认状态：** [ ] 待确认 [ ] 已确认
- **确认日期：** YYYY-MM-DD
- **用户签名/确认记录：** [留空待用户处理或记录用户明确同意的文本]
