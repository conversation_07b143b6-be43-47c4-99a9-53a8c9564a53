---
type: "manual"
---

# Python 单元测试编写指南

## 文档信息

| 项目 | 内容 |
|---|---|
| 文档名称 | Python 单元测试编写指南 |
| 文档版本 | v2.0.0 |
| 生效日期 | 2025-06-24 |
| 适用范围 | 所有需要编写单元测试的Python项目 |
| 核心依赖 | [编码规范.md](./编码规范.md) |
| 维护人 | 研发团队 |

## 目录

1. [概述](#1-概述)
    1. [目的](#11-目的)
    2. [核心原则](#12-核心原则)
2. [测试基础](#2-测试基础)
    1. [测试结构与位置](#21-测试结构与位置)
    2. [命名规范](#22-命名规范)
    3. [模块导入](#23-模块导入)
3. [测试用例编写模式：Arrange-Act-Assert (AAA)](#3-测试用例编写模式arrange-act-assert-aaa)
    1. [Arrange-Act-Assert (AAA)](#31-arrange-act-assert-aaa)
    2. [一个简单的例子](#32-一个简单的例子)
4. [断言：验证行为](#4-断言验证行为)
    1. [使用标准 `assert`](#41-使用标准-assert)
    2. [测试浮点数 (`pytest.approx`)](#42-测试浮点数-pytestapprox)
    3. [测试异常 (`pytest.raises`)](#43-测试异常-pytestraises)
    4. [测试日志输出 (`caplog`)](#44-测试日志输出-caplog)
5. [Fixtures：管理测试依赖与状态](#5-fixtures管理测试依赖与状态)
    1. [为何需要 Fixture](#51-为何需要-fixture)
    2. [Fixture 的基本用法](#52-fixture-的基本用法)
    3. [Fixture 的作用域 `scope`](#53-fixture-的作用域-scope)
    4. [使用 Fixture 执行 setup/teardown](#54-使用-fixture-执行-setupteardown)
    5. [常用的内置 Fixtures](#55-常用的内置-fixtures)
6. [参数化：用不同数据运行同一测试](#6-参数化用不同数据运行同一测试)
    1. [基本参数化 (`@pytest.mark.parametrize`)](#61-基本参数化-pytestmarkparametrize)
    2. [为参数化测试用例添加 ID](#62-为参数化测试用例添加-id)
7. [Mocking：隔离被测单元](#7-mocking隔离被测单元)
    1. [何时使用 Mock](#71-何时使用-mock)
    2. [使用 `pytest-mock` (`mocker` fixture)](#72-使用-pytest-mock-mocker-fixture)
    3. [验证 Mock 调用](#73-验证-mock-调用)
    4. [Mock Patching 的最佳实践](#74-mock-patching-的最佳实践)
8. [测试用例设计策略](#8-测试用例设计策略)
    1. [测试什么？](#81-测试什么)
    2. [测试用例分类](#82-测试用例分类)
9. [测试执行与质量门禁](#9-测试执行与质量门禁)
    1. [运行测试](#91-运行测试)
    2. [测试覆盖率](#92-测试覆盖率)
    3. [测试完成标准](#93-测试完成标准)
10. [文档化你的测试](#10-文档化你的测试)
11. [附录：Pull Request 测试检查清单](#11-附录pull-request-测试检查清单)
12. [文档变更历史](#12-文档变更历史)

---

## 1. 概述

### 1.1 目的
本指南旨在为开发团队提供一套关于单元测试的统一标准、最佳实践和实用方法。目标是确保所有单元测试都具有高质量、高可读性、高可维护性，从而保障项目代码的稳定性和可靠性。

### 1.2 核心原则
所有单元测试的编写都必须严格遵守以下核心原则：
*   **【强制】统一测试框架**: 项目统一使用 `pytest` 作为唯一的单元测试框架，并禁止使用 `unittest`、`nose` 等其他框架。
*   **【强制】遵循编码规范**: 本文档是 **[编码规范.md](./编码规范.md)** 的配套文档，所有测试代码必须严格遵循编码规范中的所有相关规定。
*   **【强制】测试必须是可重复的**: 测试不应依赖于外部环境（如网络、特定文件），并且在任何时间、任何地点运行都应得到相同的结果。
*   **【强制】测试必须是独立的**: 每个测试用例应独立于其他测试用例，不应依赖于执行顺序或共享状态。
*   **【强制】测试必须是透明的**: 测试的意图应清晰明了，代码应易于理解。

## 2. 测试基础

### 2.1 测试结构与位置
*   **【强制】** 所有单元测试代码均应放置在项目根目录下的 `tests/` 目录中。
*   **【强制】** `tests/` 目录内部的组织结构应与 `src/` 目录中被测试代码的结构保持一致。

**示例:**
```
your_project/
├── src/
│   ├── __init__.py
│   ├── data/
│   │   ├── __init__.py
│   │   └── processor.py
│   └── utils/
│       ├── __init__.py
│       └── string_helper.py
└── tests/
    ├── __init__.py
    ├── data/
    │   ├── __init__.py
    │   └── test_processor.py
    └── utils/
        ├── __init__.py
        └── test_string_helper.py
```

### 2.2 命名规范
*   **测试文件**: 必须以 `test_` 开头 (例如, `test_string_helper.py`)。
*   **测试函数**: 必须以 `test_` 开头 (例如, `def test_reverses_string_correctly():`)。
*   **【推荐】测试函数名应清晰描述其行为**: 例如，`test_user_creation_fails_if_email_is_invalid()` 优于 `test_create_user()`。

### 2.3 模块导入
*   **【强制】严禁修改 `sys.path`**: 禁止在测试代码中使用 `sys.path.append()` 或类似技巧来导入 `src` 中的模块。
*   **【标准做法】使用可编辑模式安装**: 在项目根目录下，通过运行 `pip install -e .` 来安装项目。这使得 `src` 包对 Python 解释器（包括 `pytest`）全局可见。
*   **【强制】使用绝对导入**: 测试代码必须使用标准的绝对导入语句来导入被测代码。
    ```python
    # 正确
    from src.utils.string_helper import reverse_string

    # 错误
    # from ..src.utils.string_helper import reverse_string
    ```

## 3. 测试用例编写模式：Arrange-Act-Assert (AAA)

### 3.1 Arrange-Act-Assert (AAA)
AAA 是一个组织测试用例的经典模式，能极大地提高测试的可读性。
*   **Arrange (准备)**: 准备测试所需的所有前提条件。这包括初始化对象、设置 Mock、准备测试数据等。
*   **Act (执行)**: 执行被测试的代码或函数。这通常是测试的核心，只有一行代码。
*   **Assert (断言)**: 验证执行结果是否符合预期。可以有一条或多条断言。

### 3.2 一个简单的例子
**被测试函数 (`src/utils/string_helper.py`)**:
```python
def reverse_string(s: str) -> str:
    """Reverses a given string."""
    if not isinstance(s, str):
        raise TypeError("Input must be a string.")
    return s[::-1]
```

**测试用例 (`tests/utils/test_string_helper.py`)**:
```python
from src.utils.string_helper import reverse_string

def test_reverse_string_with_valid_string():
    """
    Tests that a valid string is correctly reversed.
    """
    # Arrange
    input_string = "hello"
    expected_output = "olleh"

    # Act
    actual_output = reverse_string(input_string)

    # Assert
    assert actual_output == expected_output
```

## 4. 断言：验证行为

`pytest` 允许使用 Python 原生的 `assert` 语句，并提供了丰富的上下文信息。

### 4.1 使用标准 `assert`
你可以对任何表达式进行断言。
```python
def test_list_append():
    # Arrange
    my_list = [1, 2, 3]

    # Act
    my_list.append(4)

    # Assert
    assert len(my_list) == 4
    assert my_list == [1, 2, 3, 4]
```

### 4.2 测试浮点数 (`pytest.approx`)
由于浮点数精度问题，不应直接使用 `==` 进行比较。
```python
import pytest

def test_float_division():
    assert (0.1 + 0.2) == pytest.approx(0.3)
```

### 4.3 测试异常 (`pytest.raises`)
使用 `pytest.raises` 上下文管理器来断言代码块是否抛出了预期的异常。
```python
import pytest
from src.utils.string_helper import reverse_string

def test_reverse_string_raises_error_for_non_string_input():
    """
    Tests that a TypeError is raised for non-string input.
    """
    # Arrange
    non_string_input = 123

    # Act & Assert
    with pytest.raises(TypeError) as exc_info:
        reverse_string(non_string_input)

    # 可选：检查异常信息
    assert "Input must be a string" in str(exc_info.value)
```
**【强制】** `with pytest.raises(...)` 块中应只包含预期会抛出异常的那一行代码。

### 4.4 测试日志输出 (`caplog`)
使用内置的 `caplog` fixture 来捕获和验证日志信息。
```python
import logging

def my_function(logger):
    logger.warning("This is a warning.")

def test_my_function_logs_warning(caplog):
    # Arrange
    logger = logging.getLogger("test_logger")

    # Act
    with caplog.at_level(logging.WARNING):
        my_function(logger)

    # Assert
    assert "This is a warning." in caplog.text
    assert len(caplog.records) == 1
    assert caplog.records[0].levelname == "WARNING"
```

## 5. Fixtures：管理测试依赖与状态

Fixtures 是 `pytest` 的核心功能，用于提供测试所需的数据、对象、服务或任何前提条件。

### 5.1 为何需要 Fixture
*   **代码复用**: 避免在多个测试中重复编写相同的 `Arrange` 代码。
*   **解耦**: 将测试设置与测试逻辑分离，使测试更清晰。
*   **生命周期管理**: 可靠地执行 setup 和 teardown 逻辑。

### 5.2 Fixture 的基本用法
使用 `@pytest.fixture` 装饰器定义一个 fixture，然后在测试函数中像参数一样使用它。
```python
import pytest

@pytest.fixture
def sample_user_data() -> dict:
    """A fixture that provides a sample user dictionary."""
    return {"username": "testuser", "email": "<EMAIL>", "is_active": True}

def test_user_is_active(sample_user_data):
    # Arrange (由 fixture 完成)

    # Act
    is_active = sample_user_data.get("is_active", False)

    # Assert
    assert is_active is True
```

### 5.3 Fixture 的作用域 `scope`
Fixture 可以通过 `scope` 参数设置为在不同级别共享，以提高效率。
*   `function` (默认): 每个测试函数运行一次。
*   `class`: 每个测试类运行一次。
*   `module`: 每个模块运行一次。
*   `session`: 整个测试会话运行一次。

```python
import pytest

@pytest.fixture(scope="module")
def db_connection():
    """Module-scoped fixture to set up a database connection."""
    print("\nSetting up DB connection...")
    # conn = ...
    yield # conn
    print("\nTearing down DB connection...")
    # conn.close()
```

### 5.4 使用 Fixture 执行 setup/teardown
使用 `yield` 关键字可以将 fixture 分为 setup 和 teardown 两部分。`yield` 之前的代码是 setup，`yield` 之后的代码是 teardown。

### 5.5 常用的内置 Fixtures
`pytest` 提供了许多强大的内置 Fixtures，可以直接使用。
*   `tmp_path`: 创建一个临时目录，用于需要读写文件的测试。
*   `monkeypatch`: 安全地修改或替换类、方法、环境变量等，测试结束后自动恢复。
*   `capsys`: 捕获标准输出和标准错误。
*   `caplog`: 捕获日志输出。
*   `mocker`: `pytest-mock` 插件提供的 fixture，用于创建和管理 mock 对象。

## 6. 参数化：用不同数据运行同一测试

### 6.1 基本参数化 (`@pytest.mark.parametrize`)
当你想用多组不同的输入和预期输出来测试同一个函数时，参数化非常有用。
```python
import pytest

# (假设 has_special_chars 函数已存在于 src.utils.string_helper)
from src.utils.string_helper import has_special_chars

@pytest.mark.parametrize("input_string, expected", [
    ("hello", False),
    ("hello!", True),
    ("word_with_underscore", True),
    ("123", False),
    ("", False)
])
def test_has_special_chars(input_string, expected):
    assert has_special_chars(input_string) == expected
```

### 6.2 为参数化测试用例添加 ID
使用 `ids` 参数可以为每组参数提供一个描述性的名称，便于识别失败的测试。
```python
@pytest.mark.parametrize(
    "input_string, expected",
    [
        ("hello", False),
        ("hello!", True),
    ],
    ids=["no_special_chars", "with_special_chars"]
)
def test_has_special_chars_with_ids(input_string, expected):
    assert has_special_chars(input_string) == expected
```

## 7. Mocking：隔离被测单元

Mocking 是指使用模拟对象（Mock）来替代真实对象，以隔离被测单元，使其不受外部依赖（如数据库、API、文件系统）的影响。

### 7.1 何时使用 Mock
*   当被测代码依赖于**外部系统**（数据库、网络API、第三方服务）。
*   当被测代码依赖于一个**难以创建或配置**的对象。
*   当你想验证被测代码是否以**正确的方式调用**了其依赖。

### 7.2 使用 `pytest-mock` (`mocker` fixture)
`pytest-mock` 插件提供了 `mocker` fixture，它是 `unittest.mock` 的一个封装。
*   **`mocker.patch()`**: 替换一个对象。
*   **`mocker.spy()`**: "监视"一个对象的方法调用，但不改变其行为。
*   **`mocker.MagicMock()`**: 创建一个灵活的 mock 对象。

**示例：Mock 一个 API 调用**
```python
import requests

def get_user_full_name(user_id: int):
    """Fetches user data from an external API."""
    response = requests.get(f"https://api.example.com/users/{user_id}")
    response.raise_for_status()
    user_data = response.json()
    return f"{user_data['first_name']} {user_data['last_name']}"

def test_get_user_full_name(mocker):
    # Arrange
    mock_response = mocker.MagicMock()
    mock_response.json.return_value = {"first_name": "John", "last_name": "Doe"}
    mock_response.raise_for_status.return_value = None

    mocker.patch("requests.get", return_value=mock_response)

    # Act
    full_name = get_user_full_name(1)

    # Assert
    assert full_name == "John Doe"
```

### 7.3 验证 Mock 调用
除了验证返回值，验证依赖是否被正确调用也很重要。
```python
def test_get_user_full_name_calls_api_correctly(mocker):
    # Arrange
    mock_get = mocker.patch("requests.get")
    # ... (setup mock_response as before)
    mock_get.return_value = ...

    # Act
    get_user_full_name(1)

    # Assert
    mock_get.assert_called_once_with("https://api.example.com/users/1")
```

### 7.4 Mock Patching 的最佳实践
**【强制】** `patch` 的目标字符串应该是**被测代码中实际查找和使用依赖的地方**，而不是依赖的原始来源。
*   如果 `src.logic.main` 中有 `from src.utils import helper`，然后在函数中调用 `helper.do_something()`，那么正确的 patch 目标是 `'src.logic.main.helper.do_something'`，而不是 `'src.utils.helper.do_something'`。

## 8. 测试用例设计策略

### 8.1 测试什么？
*   **公共API**: 测试模块的公共函数和类的公共方法。
*   **业务逻辑**: 测试核心算法和业务规则。
*   **数据处理**: 测试数据的转换、验证和处理。
*   **与其他单元的交互**: 测试代码是否正确调用其依赖（通常通过 Mock）。
*   **不测试什么？**:
    *   私有方法（`_private_method`）：私有方法应通过测试调用它们的公共方法来间接测试。
    *   第三方库的内部实现：只需 mock 它们的接口，并相信它们是正确的。

### 8.2 测试用例分类
一个健壮的测试套件应覆盖以下三种情况：
1.  **正常情况 (Happy Path)**: 使用有效、典型的输入，验证函数是否返回预期的结果。
2.  **边界情况 (Edge Cases)**: 测试输入的边界值，例如空字符串、空列表、0、负数、最大/最小值等。
3.  **异常情况 (Error Cases)**: 提供无效的输入（错误类型、格式错误等），验证函数是否按预期失败（例如，抛出正确的异常）。

## 9. 测试执行与质量门禁

### 9.1 运行测试
*   运行所有测试: `pytest` 或 `pytest tests/`
*   运行特定文件: `pytest tests/utils/test_string_helper.py`
*   运行特定测试: `pytest tests/utils/test_string_helper.py::test_reverse_string_with_valid_string`
*   使用关键字匹配: `pytest -k "reverse_string"`

### 9.2 测试覆盖率
*   **【强制】** 项目必须配置 `pytest-cov` 来监控代码覆盖率。
*   运行带覆盖率报告的测试： `pytest --cov=src tests/`
*   **【质量门禁】** 新代码的行覆盖率（Line Rate）不得低于 **85%**。对核心业务逻辑，应追求更高的覆盖率。

### 9.3 测试完成标准
**【强制】** 在编写或修改任何单元测试后，必须在本地运行所有相关测试，并确保它们 **100% 通过 (PASSED)**。只有在此之后，工作才被视为完成。严禁提交未通过或被跳过 (`SKIPPED`) 的测试代码到主开发分支。

## 10. 文档化你的测试

**【强制】** 测试代码也是项目的重要组成部分，必须像生产代码一样编写清晰的文档。
*   **模块级文档字符串**: 每个 `test_*.py` 文件都应在文件顶部包含一个模块级文档字符串，说明该测试模块的目标（即，它在测试哪个生产模块的功能）。
*   **函数级文档字符串**:
    *   对于简单的测试用例，一个描述性的函数名可能足够了。
    *   对于复杂的测试（例如，有复杂 `Arrange` 步骤的测试）或 Fixtures，**必须**编写符合 **Google Python Style** 的文档字符串，清晰地解释测试的目的、设置和预期结果。

## 11. 附录：Pull Request 测试检查清单
在提交 Pull Request 进行代码审查之前，请确认以下事项：
- [ ] 所有新代码都有对应的单元测试。
- [ ] 所有新/修改的测试都在本地通过。
- [ ] 测试覆盖率满足项目要求（>85%）。
- [ ] 测试代码遵循了本文档和 `编码规范.md` 的所有规定。
- [ ] 测试用例覆盖了正常、边界和异常情况。
- [ ] 对于复杂的测试，已添加了清晰的文档字符串。

## 12. 文档变更历史

| 版本 | 日期 | 变更内容 | 修改人 |
|---|---|---|---|
| v2.0.0 | 2025-06-24 | 首次发布，全面重构和内容扩充，引入AAA、Fixtures、Mocking、参数化等最佳实践。 | 研发团队 |
| v1.0.0 | 2025-06-22 | 初始草稿版本。 | 研发团队 |
